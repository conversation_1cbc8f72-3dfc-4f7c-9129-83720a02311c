// ******************************************************
// 视频分析服务 - 纯推送架构
// 
// 设计理念：
//   1. 简洁优雅 - 不返回值，直接推送结果
//   2. 最佳实践 - 充分利用异步框架机制
//   3. 扁平化风格 - 减少嵌套，逻辑清晰
//   4. 注释清晰 - 每个方法都有明确的职责说明
// ******************************************************

use std::path::Path;
use std::sync::Arc;
use log::{info, error};
use serde_json::json;
use crate::ipc::{TcpDuplexServer, success, error as ipc_error};



/// 视频分析服务 - 纯推送架构
/// 
/// 职责：分析视频元数据并直接推送结果到前端
/// 特点：不返回值，所有结果通过命名管道推送
#[derive(Clone)]
pub struct VideoAnalysisService {
    /// TCP全双工服务器 - 推送通道
    tcp_server: Arc<TcpDuplexServer>,
}

impl VideoAnalysisService {
    /// 创建新的视频分析服务
    pub fn new(tcp_server: Arc<TcpDuplexServer>) -> Self {
        Self { tcp_server }
    }
    
    /// 分析视频文件 - 推送结果
    /// 
    /// 分析视频的元数据信息，包括分辨率、时长、编码格式等
    /// 结果直接推送到前端，不返回值
    pub async fn analyze_video(&self, video_path: &str) {
        info!("🔍 开始分析视频: {}", video_path);
        
        // 检查文件是否存在
        if !Path::new(video_path).exists() {
            error!("❌ 视频文件不存在: {}", video_path);
            // 直接使用统一函数推送错误
            self.tcp_server.send_video_analysis(ipc_error("找不到视频文件，请确认文件路径是否正确".to_string())).await;
            return;
        }
        
        // 调用FFmpeg分析器
        match crate::ffmpeg::FFmpegAnalyzer::analyze(video_path) {
            Ok(metadata) => {
                info!("✅ 视频分析完成: {}", video_path);
                // 直接使用统一函数推送结果
                self.tcp_server.send_video_analysis(success(json!({
                    "video_path": video_path,
                    "metadata": {
                        "resolution": metadata.resolution,
                        "duration": metadata.duration,
                        "bitrate": metadata.bitrate,
                        "frame_rate": metadata.frame_rate,
                        "has_subtitles": metadata.has_subtitles
                    },
                    "analysis_time": chrono::Utc::now().to_rfc3339()
                }))).await;
            }
            Err(error) => {
                error!("❌ 视频分析失败: {} - {}", video_path, error);
                // 直接使用统一函数推送错误
                self.tcp_server.send_video_analysis(ipc_error("无法分析视频文件，请确认文件格式是否受支持".to_string())).await;
            }
        }
    }
    
    /// 检查GPU加速支持 - 推送结果
    ///
    /// 检查指定GPU类型是否支持硬件加速（与原版本功能一致）
    /// 结果直接推送到前端，不返回值
    pub async fn check_gpu_acceleration(&self, gpu_type: &str) {
        info!("🚀 开始检查GPU加速: {}", gpu_type);

        // 获取FFmpeg路径
        let config = crate::utils::get_config();
        let ffmpeg_path = std::path::Path::new(&config.ffmpeg_path);

        // 使用原来的GPU检查实现
        let result = crate::ffmpeg::gpu_config::GpuConfigManager::check_gpu_available(gpu_type, ffmpeg_path);

        // 直接推送结果
        self.tcp_server.send_gpu_check(result).await;

        info!("✅ GPU加速检查完成: {}", gpu_type);
    }


}
