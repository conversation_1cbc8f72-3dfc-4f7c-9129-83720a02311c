// ******************************************************
// 视频预览服务 - 纯推送架构
// 
// 设计理念：
//   1. 简洁优雅 - 不返回值，直接推送结果
//   2. 最佳实践 - 充分利用异步框架机制
//   3. 扁平化风格 - 减少嵌套，逻辑清晰
//   4. 注释清晰 - 每个方法都有明确的职责说明
//   5. 自动清理 - 临时文件自动管理
// ******************************************************

use std::path::Path;
use std::sync::Arc;
use log::{info, error};
use serde_json::json;
use crate::ipc::{TcpDuplexServer, success, error as ipc_error};
use crate::models::effect_params::{EffectParam, EffectCommandGenerator};
use crate::ffmpeg::FFmpegExecutor;

/// 视频预览服务 - 纯推送架构
/// 
/// 职责：生成视频效果预览并直接推送结果到前端
/// 特点：不返回值，所有结果通过命名管道推送，自动清理临时文件
#[derive(Clone)]
pub struct VideoPreviewService {
    /// TCP全双工服务器 - 推送通道
    tcp_server: Arc<TcpDuplexServer>,
}

impl VideoPreviewService {
    /// 创建新的视频预览服务
    pub fn new(tcp_server: Arc<TcpDuplexServer>) -> Self {
        Self { tcp_server }
    }
    
    /// 预览视频效果 - 推送结果
    /// 
    /// 生成指定效果的预览帧序列，结果直接推送到前端
    /// 自动清理临时文件，不返回值
    pub async fn preview_effect(&self, video_path: &str, effect: &EffectParam) {
        info!("👁️ 开始生成效果预览: {} - {:?}", video_path, effect.effect_type);
        
        // 检查视频文件是否存在
        if !Path::new(video_path).exists() {
            error!("❌ 视频文件不存在: {}", video_path);
            // 直接使用统一函数推送错误
            self.tcp_server.send_preview(ipc_error("找不到视频文件，请确认文件路径是否正确".to_string())).await;
            return;
        }
        
        // 清理旧的临时文件
        crate::utils::clear_temp_files();
        
        // 创建预览目录
        let frames_dir = match self.create_preview_directory() {
            Ok(dir) => dir,
            Err(error) => {
                error!("❌ 创建预览目录失败: {}", error);
                // 直接使用统一函数推送错误
                self.tcp_server.send_preview(ipc_error("无法创建临时目录，请检查磁盘空间或权限设置".to_string())).await;
                return;
            }
        };
        
        // 生成效果滤镜
        let effect_filter = match EffectCommandGenerator::generate_filter(effect) {
            Ok(filter) => filter,
            Err(error) => {
                error!("❌ 效果滤镜生成失败: {}", error);
                // 直接使用统一函数推送错误
                self.tcp_server.send_preview(ipc_error("效果参数配置无效，请检查参数设置".to_string())).await;
                return;
            }
        };
        
        // 生成预览帧
        match self.generate_preview_frames(video_path, &frames_dir, &effect_filter).await {
            Ok(_) => {
                info!("✅ 效果预览生成完成: {}", video_path);
                // 直接使用统一函数推送结果
                self.tcp_server.send_preview(success(json!({
                    "frames_directory": frames_dir
                }))).await;
                info!("📤 预览结果已推送: {}", frames_dir);
            }
            Err(error) => {
                error!("❌ 效果预览生成失败: {} - {}", video_path, error);
                // 直接使用统一函数推送错误
                self.tcp_server.send_preview(ipc_error("预览生成失败，请稍后重试".to_string())).await;
            }
        }
    }
    

    
    /// 生成预览帧（内部实现）
    async fn generate_preview_frames(&self, video_path: &str, output_dir: &str, effect_filter: &str) -> Result<(), String> {
        // 使用FFmpeg生成预览帧（保持与原版本一致的参数）
        FFmpegExecutor::generate_preview_frames(
            video_path,
            output_dir,
            5.0,  // 5秒预览时长（与原版本一致）
            30,   // 30fps帧率（与原版本一致）
            Some(effect_filter),
        ).await.map(|_| ()) // 忽略返回的路径，只关心成功或失败
    }
    
    /// 创建预览目录（内部实现）
    fn create_preview_directory(&self) -> Result<String, std::io::Error> {
        let config = crate::utils::get_config();

        // 使用时间戳作为目录名
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis();

        let frames_dir = config.temp_dir.join(format!("preview_frames_{}", timestamp));
        let frames_dir_str = frames_dir.to_string_lossy().to_string();

        // 只处理技术操作，错误消息在调用处统一处理
        std::fs::create_dir_all(&frames_dir)?;

        Ok(frames_dir_str)
    }
    

    

}


