// ******************************************************
// 文件名: Converters.cs
// 功能描述: 值转换器集合
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 提供各种数据类型的值转换器
//   2. 支持UI绑定时的数据转换
//   3. 实现特殊的显示逻辑转换
// ******************************************************

using Avalonia.Data.Converters;
using System;
using System.Globalization;
using Avalonia.Controls;
using Avalonia.Media;
using Avalonia;
using System.ComponentModel;
using System.Threading.Tasks;
using Avalonia.Animation;
using Avalonia.Animation.Easings;
using Avalonia.Styling;

namespace ECutPro.Converters;

/// <summary>
/// 提供双精度值转换器的静态类
/// </summary>
public static class DoubleConverters
{
    // ====================== 静态转换器 ======================
    
    /// <summary>
    /// 将双精度值转换为百分比字符串
    /// </summary>
    public static readonly IValueConverter ToPercentage = new FuncValueConverter<double, string>(value => 
    {
        // 将0-100的值转换为CSS百分比字符串
        double percent = Math.Clamp(value, 0, 100);
        return $"{percent}%";
    });
}

/// <summary>
/// 将进度值(0-100)转换为百分比宽度的转换器
/// </summary>
public class ProgressWidthConverter : IValueConverter
{
    // ====================== 转换方法 ======================
    
    /// <summary>
    /// 将进度值(0-100)转换为百分比宽度
    /// </summary>
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is double doubleValue)
        {
            // 确保值在0-100之间
            doubleValue = Math.Clamp(doubleValue, 0, 100);
            
            // 转换为百分比(0-1)
            return doubleValue / 100.0;
        }
        
        return 0.0;
    }

    /// <summary>
    /// 不支持反向转换
    /// </summary>
    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotSupportedException("不支持反向转换");
    }
}

/// <summary>
/// 布尔值转换器静态类
/// </summary>
public static class BoolConverters
{
    // ====================== 静态转换器 ======================
    
    /// <summary>
    /// 布尔值取反转换器
    /// </summary>
    public static readonly IValueConverter Invert = new FuncValueConverter<bool, bool>(value => !value);
    
    /// <summary>
    /// 布尔值转可见性转换器
    /// </summary>
    public static readonly IValueConverter ToVisibility = new FuncValueConverter<bool, bool>(value => value);
    
    /// <summary>
    /// 布尔值转颜色转换器（启用/禁用颜色）
    /// </summary>
    public static readonly IValueConverter ToColor = new FuncValueConverter<bool, Color>(value => 
        value ? Colors.White : Color.Parse("#80FFFFFF"));
}

/// <summary>
/// 字符串转换器静态类
/// </summary>
public static class StringConverters
{
    // ====================== 静态转换器 ======================
    
    /// <summary>
    /// 字符串为空则显示替代文本转换器
    /// </summary>
    public static readonly IValueConverter EmptyToPlaceholder = new FuncValueConverter<string, string>(value => 
        string.IsNullOrWhiteSpace(value) ? "未设置" : value);
} 

/// <summary>
/// 布尔值转颜色转换器
/// </summary>
public class BoolToColorConverter : IValueConverter
{
    // ====================== 转换方法 ======================
    
    /// <summary>
    /// 将布尔值转换为颜色
    /// </summary>
    /// <param name="value">布尔值</param>
    /// <param name="targetType">目标类型</param>
    /// <param name="parameter">参数(可选，格式:"FalseColor|TrueColor")</param>
    /// <param name="culture">区域信息</param>
    /// <returns>对应的颜色画刷</returns>
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isEnabled)
        {
            if (parameter is string colorParam)
            {
                // 格式: "FalseColor|TrueColor"
                var colors = colorParam.Split('|');
                if (colors.Length == 2)
                {
                    string colorStr = isEnabled ? colors[1] : colors[0];
                    return SolidColorBrush.Parse(colorStr);
                }
            }
            
            // 默认颜色
            return isEnabled ? 
                new SolidColorBrush(Color.Parse("#30C9B0")) : 
                new SolidColorBrush(Color.Parse("#BBBBBB"));
        }
        
        return new SolidColorBrush(Color.Parse("#BBBBBB"));
    }

    /// <summary>
    /// 不支持反向转换
    /// </summary>
    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 丝滑进度更新管理器 - 最安全、最简洁、最优雅、最佳实践！
/// </summary>
public class SmoothProgressManager : INotifyPropertyChanged
{
    // ====================== 事件 ======================

    public event PropertyChangedEventHandler? PropertyChanged;

    // ====================== 私有字段 ======================

    private double _currentProgress = 0;
    private double _displayProgress = 0;
    private DateTime _lastUpdate = DateTime.Now;
    private readonly object _lock = new object();

    // ====================== 公共属性 ======================

    /// <summary>
    /// 当前显示的进度值 (0-100)
    /// </summary>
    public double DisplayProgress
    {
        get => _displayProgress;
        private set
        {
            if (Math.Abs(_displayProgress - value) > 0.01) // 避免微小变化
            {
                _displayProgress = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(DisplayProgress)));
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(DisplayProgressText)));
            }
        }
    }

    /// <summary>
    /// 进度文本显示
    /// </summary>
    public string DisplayProgressText => $"{DisplayProgress:F1}%";

    // ====================== 公共方法 ======================

    /// <summary>
    /// 丝滑更新进度 - 核心方法
    /// </summary>
    /// <param name="newProgress">新的进度值 (0-100)</param>
    public async Task UpdateProgressSmoothly(double newProgress)
    {
        lock (_lock)
        {
            // 确保进度值在有效范围内
            newProgress = Math.Clamp(newProgress, 0, 100);

            // 如果进度没有变化，直接返回
            if (Math.Abs(_currentProgress - newProgress) < 0.01)
                return;

            _currentProgress = newProgress;
        }

        // 计算智能过渡时间
        var transitionDuration = CalculateTransitionDuration(_displayProgress, newProgress);

        // 执行丝滑动画
        await AnimateToProgress(newProgress, transitionDuration);
    }

    /// <summary>
    /// 重置进度
    /// </summary>
    public void Reset()
    {
        lock (_lock)
        {
            _currentProgress = 0;
            _lastUpdate = DateTime.Now;
        }
        DisplayProgress = 0;
    }

    // ====================== 私有方法 ======================

    /// <summary>
    /// 计算智能过渡时间
    /// </summary>
    private TimeSpan CalculateTransitionDuration(double from, double to)
    {
        var progressDiff = Math.Abs(to - from);

        // 根据进度差异调整动画时间
        if (progressDiff < 1) return TimeSpan.FromMilliseconds(200);   // 小变化：快速
        if (progressDiff < 5) return TimeSpan.FromMilliseconds(500);   // 中等变化：中速
        if (progressDiff < 20) return TimeSpan.FromMilliseconds(800);  // 大变化：慢速
        return TimeSpan.FromMilliseconds(1000); // 巨大变化：最慢
    }

    /// <summary>
    /// 执行进度动画
    /// </summary>
    private async Task AnimateToProgress(double targetProgress, TimeSpan duration)
    {
        var startProgress = _displayProgress;
        var progressDiff = targetProgress - startProgress;
        var startTime = DateTime.Now;

        // 使用缓动函数实现丝滑效果
        while (DateTime.Now - startTime < duration)
        {
            var elapsed = DateTime.Now - startTime;
            var progress = elapsed.TotalMilliseconds / duration.TotalMilliseconds;

            // 使用缓出函数 (Ease Out Cubic)
            var easedProgress = 1 - Math.Pow(1 - progress, 3);

            var currentValue = startProgress + (progressDiff * easedProgress);
            DisplayProgress = Math.Clamp(currentValue, 0, 100);

            await Task.Delay(16); // 60 FPS 更新
        }

        // 确保最终值准确
        DisplayProgress = targetProgress;
    }
}
