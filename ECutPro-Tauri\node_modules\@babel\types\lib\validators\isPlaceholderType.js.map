{"version": 3, "names": ["_index", "require", "isPlaceholderType", "placeholderType", "targetType", "aliases", "PLACEHOLDERS_ALIAS", "includes"], "sources": ["../../src/validators/isPlaceholderType.ts"], "sourcesContent": ["import { PLACEHOLDERS_ALIAS } from \"../definitions/index.ts\";\n\n/**\n * Test if a `placeholderType` is a `targetType` or if `targetType` is an alias of `placeholderType`.\n */\nexport default function isPlaceholderType(\n  placeholderType: string,\n  targetType: string,\n): boolean {\n  if (placeholderType === targetType) return true;\n\n  const aliases: Array<string> | undefined =\n    PLACEHOLDERS_ALIAS[placeholderType];\n  if (aliases?.includes(targetType)) return true;\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAKe,SAASC,iBAAiBA,CACvCC,eAAuB,EACvBC,UAAkB,EACT;EACT,IAAID,eAAe,KAAKC,UAAU,EAAE,OAAO,IAAI;EAE/C,MAAMC,OAAkC,GACtCC,yBAAkB,CAACH,eAAe,CAAC;EACrC,IAAIE,OAAO,YAAPA,OAAO,CAAEE,QAAQ,CAACH,UAAU,CAAC,EAAE,OAAO,IAAI;EAE9C,OAAO,KAAK;AACd", "ignoreList": []}