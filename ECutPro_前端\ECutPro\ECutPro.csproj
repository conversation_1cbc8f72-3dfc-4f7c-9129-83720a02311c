<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    
    <!-- 程序集名称和产品信息 -->
    <AssemblyName>ECutPro</AssemblyName>
    <Product>E剪 Pro</Product>
    <Description>专业视频批量处理工具</Description>

    <!-- AOT配置 - 全局启用 -->
    <PublishAot>true</PublishAot>
    <TrimMode>full</TrimMode>  <!-- 完全裁剪 -->
    
    <!-- 代码保护相关 -->
   <!-- Debug模式保留调试符号 -->
    <StripSymbols Condition="'$(Configuration)' != 'Debug'">true</StripSymbols>
    <IlcTrimMetadata>true</IlcTrimMetadata>  <!-- 裁剪元数据 -->
    <IlcFoldIdenticalMethodBodies>true</IlcFoldIdenticalMethodBodies>  <!-- 合并相同方法体 -->
    
    <!-- 性能优化 -->
    <OptimizationPreference>Size</OptimizationPreference>
    
    <!-- 最强安全设置 -->
    <IlcDisableReflection>false</IlcDisableReflection>  <!-- 禁用反射 -->
    <JsonSerializerIsReflectionEnabledByDefault>true</JsonSerializerIsReflectionEnabledByDefault>
  </PropertyGroup>


  <!-- AOT裁剪配置 - 全局启用 -->
  <ItemGroup>
    <TrimmerRootAssembly Include="ECutPro" />  <!-- 确保你的代码被完整保留并AOT编译 -->
  </ItemGroup>
  
  <!-- 禁用动态代码执行的功能，提高AOT兼容性 -->
  <ItemGroup>
    <RuntimeHostConfigurationOption Include="System.Runtime.Loader.UseRidGraph" Value="true" /> <!-- 使用RID图而不是动态加载 -->
    <RuntimeHostConfigurationOption Include="System.Reflection.Metadata.MetadataUpdater.IsSupported" Value="false" /> <!-- 禁用元数据更新器 -->
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

  <!-- 确保动画帧文件被复制到输出目录 -->
  <ItemGroup>
    <!-- 将动画帧文件嵌入到程序集中 -->
    <EmbeddedResource Include="Assets\loading\**">
      <LogicalName>ECutPro.Assets.loading.%(Filename)%(Extension)</LogicalName>
    </EmbeddedResource>
    
    <!-- 其他内容文件继续保持复制 -->
    <Content Include="Resources\FFmpeg\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>  <!-- 复制到输出目录 -->
    </Content>
  </ItemGroup>

  <!-- 确保FFmpeg资源文件被复制到输出目录 -->
  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.2" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.2" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.2" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.2" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Include="Avalonia.Diagnostics" Version="11.3.2">
      <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
      <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
    </PackageReference>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="LiteDB" Version="5.0.21" />
    <!-- H.Pipes依赖已移除，改用原生TCP Socket -->
  </ItemGroup>
</Project>

