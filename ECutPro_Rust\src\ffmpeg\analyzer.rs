use std::process::{Command, Stdio};
use std::path::Path;
use crate::utils::get_config;
use serde::{Deserialize, Serialize};

/// 视频元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoMetadata {
    /// 分辨率
    pub resolution: String,

    /// 比特率
    pub bitrate: String,

    /// 帧率
    pub frame_rate: String,

    /// 是否有字幕
    pub has_subtitles: String,

    /// 时长
    pub duration: String,
}

/// FFmpeg视频分析器
pub struct FFmpegAnalyzer;

impl FFmpegAnalyzer {
    /// 分析视频文件（纯技术分析，不记录日志）
    ///
    /// # 返回
    /// * `Ok(VideoMetadata)` - 分析成功，返回元数据
    /// * `Err(String)` - 分析失败，返回技术错误信息
    pub fn analyze(video_path: &str) -> Result<VideoMetadata, String> {
        // 检查文件是否存在
        if !Path::new(video_path).exists() {
            return Err("file_not_found".to_string());
        }
        
        // 获取ffprobe路径 - 与ffmpeg在同一目录
        let config = get_config();
        let ffprobe_path = match config.ffmpeg_path.parent() {
            Some(path) => path.join("ffprobe.exe"),
            None => return Err("ffmpeg_path_invalid".to_string())
        };

        // 检查ffprobe是否存在
        if !ffprobe_path.exists() {
            return Err("ffprobe_not_found".to_string());
        }
        
        // 构建命令
        let output = match Command::new(&ffprobe_path)
            .args(&[
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output() {
                Ok(output) => output,
                Err(e) => return Err(format!("ffprobe_execution_failed: {}", e))
            };

        // 检查执行结果
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(format!("ffprobe_command_failed: {}", stderr));
        }

        // 解析JSON输出
        let json_output = String::from_utf8_lossy(&output.stdout);
        let probe_data: serde_json::Value = match serde_json::from_str(&json_output) {
            Ok(data) => data,
            Err(e) => return Err(format!("json_parse_failed: {}", e))
        };
        
        // 提取视频流信息
        let video_stream = probe_data["streams"]
            .as_array()
            .and_then(|streams| streams.iter().find(|s| s["codec_type"] == "video"));
        
        // 提取音频流信息（未使用但保留以便将来扩展）
        let _audio_stream = probe_data["streams"]
            .as_array()
            .and_then(|streams| streams.iter().find(|s| s["codec_type"] == "audio"));
        
        // 提取字幕流信息
        let subtitle_streams = probe_data["streams"]
            .as_array()
            .map(|streams| streams.iter().filter(|s| s["codec_type"] == "subtitle").count())
            .unwrap_or(0);
        
        // 提取格式信息
        let format = &probe_data["format"];
        
        // 构建视频元数据
        let metadata = VideoMetadata {
            resolution: video_stream
                .and_then(|s| {
                    let width = s["width"].as_u64()?;
                    let height = s["height"].as_u64()?;
                    Some(format!("{}x{}", width, height))
                })
                .unwrap_or_else(|| "未知".to_string()),
                
            bitrate: format["bit_rate"]
                .as_str()
                .and_then(|br| br.parse::<u64>().ok())
                .map(|br| {
                    if br >= 1_000_000 {
                        format!("{:.2} Mbps", br as f64 / 1_000_000.0)
                    } else {
                        format!("{} Kbps", br / 1_000)
                    }
                })
                .unwrap_or_else(|| "未知".to_string()),
                
            frame_rate: video_stream
                .and_then(|s| {
                    let frame_rate = s["r_frame_rate"].as_str()?;
                    if let Some((num, den)) = frame_rate.split_once('/') {
                        let num: f64 = num.parse().ok()?;
                        let den: f64 = den.parse().ok()?;
                        if den > 0.0 {
                            Some(format!("{:.2} fps", num / den))
                        } else {
                            None
                        }
                    } else {
                        None
                    }
                })
                .unwrap_or_else(|| "未知".to_string()),
                
            has_subtitles: if subtitle_streams > 0 { "有".to_string() } else { "无".to_string() },
            
            duration: format["duration"]
                .as_str()
                .and_then(|d| d.parse::<f64>().ok())
                .map(|seconds| {
                    let hours = (seconds / 3600.0) as u64;
                    let minutes = ((seconds % 3600.0) / 60.0) as u64;
                    let secs = (seconds % 60.0) as u64;
                    
                    if hours > 0 {
                        format!("{}:{:02}:{:02}", hours, minutes, secs)
                    } else {
                        format!("{}:{:02}", minutes, secs)
                    }
                })
                .unwrap_or_else(|| "未知".to_string()),
        };
        
        Ok(metadata)
    }

    /// 便捷方法：获取视频码率（用于内部配置，不推送）
    ///
    /// # 参数
    /// * `video_path` - 视频文件路径
    ///
    /// # 返回
    /// * `Some(u32)` - 码率值（kbps）
    /// * `None` - 获取失败或文件不存在
    pub fn get_bitrate(video_path: &str) -> Option<u32> {
        match Self::analyze(video_path) {
            Ok(metadata) => {
                // 解析码率字符串，提取数字部分
                metadata.bitrate
                    .split_whitespace()
                    .next()
                    .and_then(|s| s.parse().ok())
            }
            Err(_) => None,
        }
    }
}