{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 7734858640948115565, "deps": [[9620753569207166497, "zerovec_derive", false, 17625447313343098519], [10706449961930108323, "yoke", false, 9802654011169375871], [17046516144589451410, "zerofrom", false, 10358248183832962207]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-2a1ef4947bced7a8\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}