cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rerun-if-changed=tauri.conf.json
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=E:\编程\E剪Pro\客户端\批量视频编辑器\重构版本\ECutPro-Tauri\src-tauri\target\debug\build\ecutpro-tauri-65e45c9390e6a87f\out\resource.lib
