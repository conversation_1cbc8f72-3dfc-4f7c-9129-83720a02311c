is_global = true
build_property.EnableAotAnalyzer = true
build_property.EnableSingleFileAnalyzer = true
build_property.EnableTrimAnalyzer = true
build_property.IncludeAllContentForSelfExtract = 
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = ECutPro
build_property.ProjectDir = E:\编程\E剪Pro\客户端\批量视频编辑器\重构版本\VideoEditor_重构\ECutPro\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[E:/编程/E剪Pro/客户端/批量视频编辑器/重构版本/VideoEditor_重构/ECutPro/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/编程/E剪Pro/客户端/批量视频编辑器/重构版本/VideoEditor_重构/ECutPro/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/编程/E剪Pro/客户端/批量视频编辑器/重构版本/VideoEditor_重构/ECutPro/Views/VideoEffectDialogs/EffectDialogWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
