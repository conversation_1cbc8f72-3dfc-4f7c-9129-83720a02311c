use std::process::{Command, Stdio};
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::process::Command as AsyncCommand;
use crate::utils::get_config;
use tokio_util::sync::CancellationToken;

/// FFmpeg进度解析器 - 专注百分比计算，扁平化风格
struct ProgressParser {
    /// 视频总时长（秒）
    duration: f64,
}

impl ProgressParser {
    /// 创建新的进度解析器
    fn new() -> Self {
        Self { duration: 0.0 }
    }

    /// 解析FFmpeg输出并返回进度百分比 - 扁平化风格
    fn parse_line(&mut self, line: &str) -> Option<f32> {
        // 跳过无关输出
        if line.starts_with("frame=") || line.starts_with("fps=") || line.starts_with("stream_") {
            return None;
        }

        // 解析时长（只在开始时解析一次）
        if self.duration == 0.0 && line.contains("Duration: ") {
            self.duration = self.extract_duration(line).unwrap_or(0.0);
            return None;
        }

        // 解析当前时间并直接计算百分比
        let current_time = if line.starts_with("out_time_ms=") {
            line[12..].parse::<f64>().ok()? / 1_000_000.0
        } else if line.starts_with("out_time=") {
            self.parse_time(&line[9..])?
        } else {
            return None;
        };

        // 直接返回百分比
        if self.duration > 0.0 {
            Some(((current_time / self.duration * 100.0).clamp(0.0, 100.0)) as f32)
        } else {
            None
        }
    }

    /// 提取时长信息 - 扁平化风格
    fn extract_duration(&self, line: &str) -> Option<f64> {
        let start = line.find("Duration: ")? + 10;
        let end = line[start..].find(',')?;
        let duration_str = &line[start..start + end];
        self.parse_time(duration_str)
    }

    /// 简化的时间解析 - 扁平化风格
    fn parse_time(&self, time_str: &str) -> Option<f64> {
        let parts: Vec<&str> = time_str.split(':').collect();
        if parts.len() != 3 { return None; }

        let hours = parts[0].parse::<f64>().ok()?;
        let minutes = parts[1].parse::<f64>().ok()?;
        let seconds = parts[2].parse::<f64>().ok()?;

        Some(hours * 3600.0 + minutes * 60.0 + seconds)
    }
}

/// FFmpeg执行器
pub struct FFmpegExecutor;

impl FFmpegExecutor {
    /// 获取FFmpeg路径（已在初始化时验证，无需重复检查）
    fn get_ffmpeg_path() -> std::path::PathBuf {
        get_config().ffmpeg_path.clone()
    }
    
    /// 准备FFmpeg命令参数
    fn prepare_command_args(args: &[String]) -> Vec<String> {
        let mut command_args: Vec<String> = args.iter().skip(1).cloned().collect(); // 跳过第一个参数 "ffmpeg"
        
        // 添加进度参数
        command_args.push("-progress".to_string());
        command_args.push("pipe:2".to_string()); // 输出到stderr
        command_args.push("-nostats".to_string()); // 禁用默认统计信息
        
        command_args
    }
    
    /// 处理进度输出
    fn handle_progress_output<F>(stderr: tokio::process::ChildStderr, progress_callback: F) 
    where
        F: Fn(f32) + Send + 'static + Clone,
    {
        let mut reader = BufReader::new(stderr).lines();

        tokio::spawn(async move {
            let mut parser = ProgressParser::new();
            // 异步处理进度输出
            while let Ok(Some(line)) = reader.next_line().await {
                if let Some(progress) = parser.parse_line(&line) {
                    progress_callback(progress);
                }
            }
        });
    }
    
    /// 设置取消监听
    fn setup_cancellation(mut stdin: tokio::process::ChildStdin, token: CancellationToken) {
        tokio::spawn(async move {
            token.cancelled().await;
            // 发送'q'命令优雅停止FFmpeg
            let _ = stdin.write_all(b"q\n").await;
            let _ = stdin.flush().await;
        });
    }


    
    /// 异步执行FFmpeg命令，带进度回调和取消支持（纯技术实现，不记录日志）
    ///
    /// # 返回
    /// * `Ok(())` - 执行成功
    /// * `Err(String)` - 执行失败，返回技术错误信息
    pub async fn execute<F>(
        args: &[String],
        progress_callback: F,
        cancellation_token: Option<CancellationToken>
    ) -> Result<(), String>
    where
        F: Fn(f32) + Send + 'static + Clone,
    {
        // 执行FFmpeg命令（日志由服务层记录）

        // 获取FFmpeg路径（已在初始化时验证）
        let ffmpeg_path = Self::get_ffmpeg_path();

        // 准备命令参数
        let command_args = Self::prepare_command_args(args);

        // 执行命令，启用stdin以便发送'q'命令
        let mut child = match AsyncCommand::new(ffmpeg_path)
            .args(&command_args)
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn() {
                Ok(child) => child,
                Err(e) => return Err(format!("ffmpeg_spawn_failed: {}", e))
            };
        
        // 处理进度输出
        if let Some(stderr) = child.stderr.take() {
            Self::handle_progress_output(stderr, progress_callback);
        }

        // 设置取消监听
        if let Some(token) = cancellation_token {
            if let Some(stdin) = child.stdin.take() {
                Self::setup_cancellation(stdin, token);
            }
        }

        // 等待命令执行完成
        let status = match child.wait().await {
            Ok(status) => status,
            Err(e) => return Err(format!("ffmpeg_wait_failed: {}", e))
        };

        // 检查执行结果
        if !status.success() {
            return Err(format!("ffmpeg_command_failed: exit_code={:?}", status.code()));
        }

        Ok(())
    }
    
    /// 生成视频预览帧序列（纯技术实现，不记录日志）
    ///
    /// 将视频的前几秒转换为PNG帧序列，用于前端预览
    ///
    /// # 参数
    /// * `input_path` - 输入视频路径
    /// * `output_dir` - 输出帧序列目录
    /// * `duration` - 提取的视频时长（秒）
    /// * `fps` - 输出帧序列的帧率
    /// * `effect_filter` - 要应用的效果滤镜
    ///
    /// # 返回
    /// * `Ok(String)` - 成功时返回帧序列目录路径
    /// * `Err(String)` - 失败时返回技术错误信息
    pub async fn generate_preview_frames(
        input_path: &str,
        output_dir: &str,
        duration: f32,
        fps: u32,
        effect_filter: Option<&str>
    ) -> Result<String, String> {
        // 生成预览帧序列（日志由服务层记录）

        // 获取FFmpeg路径（已在初始化时验证）
        let ffmpeg_path = Self::get_ffmpeg_path();

        // 创建输出目录（如果不存在）
        if let Err(e) = std::fs::create_dir_all(output_dir) {
            return Err(format!("create_output_dir_failed: {}", e));
        }
        
        // 构建基础命令
        let mut command_args = vec![
            "-i".to_string(), input_path.to_string(),
            "-t".to_string(), duration.to_string(),  // 限制处理时长
            "-vf".to_string(),
        ];
        
        // 构建过滤器字符串
        let mut filter_str = String::new();
        
        // 添加效果滤镜（如果有）
        if let Some(effect) = effect_filter {
            filter_str.push_str(effect);
            filter_str.push_str(",");
        }
        
        // 添加帧提取滤镜
        filter_str.push_str(&format!("fps={}", fps));
        
        // 将过滤器添加到命令参数
        command_args.push(filter_str);
        
        // 添加输出格式和路径 - 使用较低质量以加快生成速度
        command_args.extend_from_slice(&[
            "-q:v".to_string(), "3".to_string(),  // 较低质量 (范围1-5，1为最高质量)
            "-start_number".to_string(), "0".to_string(),
            "-f".to_string(), "image2".to_string(),
            format!("{}/frame_%04d.png", output_dir)
        ]);
        
        // 执行FFmpeg命令
        let output = match Command::new(ffmpeg_path)
            .args(&command_args)
            .output() {
                Ok(output) => output,
                Err(e) => return Err(format!("ffmpeg_execution_failed: {}", e))
            };

        // 检查执行结果
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(format!("ffmpeg_command_failed: {}", stderr));
        }

        // 返回帧序列目录路径
        Ok(output_dir.to_string())
    }
} 