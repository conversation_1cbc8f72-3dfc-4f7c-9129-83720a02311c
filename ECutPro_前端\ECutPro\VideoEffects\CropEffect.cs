// ******************************************************
// 文件名: CropEffect.cs
// 功能描述: 视频裁剪效果实现
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 提供视频裁剪效果的卡片定义
//   2. 实现视频裁剪的对话框显示和处理逻辑
// ******************************************************

using System;
using Avalonia.Controls;
using ECutPro.Views.VideoEffectDialogs;

namespace ECutPro.VideoEffects
{
    /// <summary>
    /// 视频裁剪效果
    /// </summary>
    public class CropEffect : VideoEffect
    {

        /// <summary>
        /// 卡片标题
        /// </summary>
        public override string Title => "视频裁剪";
        
        /// <summary>
        /// 卡片描述
        /// </summary>
        public override string Description => "裁剪视频尺寸和比例";
        
        /// <summary>
        /// 卡片图标路径数据
        /// </summary>
        public override string IconPath => "M2,6 L6,6 M6,2 L6,6 M10,2 L10,6 M10,6 L14,6 M10,10 L14,10 M10,10 L10,14 M6,10 L10,10 M6,10 L6,14 M2,10 L6,10";
        
        /// <summary>
        /// 卡片标识
        /// </summary>
        public override string Tag => "crop";
        
        /// <summary>
        /// 对话框宽度
        /// </summary>
        public override double DialogWidth => 650;
        
        /// <summary>
        /// 对话框高度
        /// </summary>
        public override double DialogHeight => 450;
        
        // ====================== 重写方法 ======================
        
        /// <summary>
        /// 预览效果
        /// </summary>
        protected override void PreviewEffect()
        {
            // 基础预览实现
            App.ShowToast("预览功能正在开发中，敬请期待！");
        }
        
        /// <summary>
        /// 创建对话框内容
        /// </summary>
        protected override Control CreateDialogContent()
        {
            // 这里应该创建裁剪设置的UI控件
            // 临时返回一个简单的面板
            return new Panel();
        }
    }
} 