<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="using:ECutPro"
             xmlns:conv="using:ECutPro.Converters"
             x:Class="ECutPro.App"
             RequestedThemeVariant="Dark">

    <Application.Resources>
        <conv:BoolToColorConverter x:Key="BoolToColorConverter"/>
    </Application.Resources>

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <Application.Styles>
        <FluentTheme />
        
        <!-- ====================== 功能卡片样式 ====================== -->
        
        <!-- 效果卡片基础样式 -->
        <Style Selector="Border.EffectCard">
            <Setter Property="Background" Value="#282828"/>
            <Setter Property="BorderBrush" Value="#323232"/>
            <Setter Property="BorderThickness" Value="0,0,0,0"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="BoxShadow" Value="0 1 2 0 #10000000"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="150"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="MinHeight" Value="50"/>
            <Setter Property="MaxHeight" Value="54"/>
            <Setter Property="RenderTransform" Value="none"/>
            <Setter Property="Transitions">
                <Setter.Value>
                    <Transitions>
                        <DoubleTransition Property="Opacity" Duration="0:0:0.3"/>
                        <BrushTransition Property="Background" Duration="0:0:0.3"/>
                        <BrushTransition Property="BorderBrush" Duration="0:0:0.3"/>
                        <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.3" Easing="CubicEaseOut"/>
                    </Transitions>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 效果卡片悬停样式 -->
        <Style Selector="Border.EffectCard:pointerover">
            <Setter Property="BoxShadow" Value="0 3 10 0 #30000000"/>
            <Setter Property="Background" Value="#363636"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="RenderTransform" Value="translateY(-2px)"/>
        </Style>
        
        <!-- 效果卡片激活样式 -->
        <Style Selector="Border.EffectCardActive">
            <Setter Property="Background" Value="#233238"/>
            <Setter Property="BoxShadow" Value="0 1 2 0 #10000000"/>
        </Style>
        
        <!-- 效果卡片标题和描述样式 -->
        <Style Selector="TextBlock.EffectTitle">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFFFFF"/>
        </Style>
        
        <Style Selector="TextBlock.EffectDescription">
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#BBBBBB"/>
        </Style>
        
        <Style Selector="TextBlock.EffectTitleActive">
            <Setter Property="Foreground" Value="#30C9B0"/>
        </Style>
        
        <Style Selector="TextBlock.EffectDescriptionActive">
            <Setter Property="Foreground" Value="#8FDDDD"/>
        </Style>
        
        <!-- 功能图标圆形容器样式 -->
        <Style Selector="Border.IconContainer">
            <Setter Property="Width" Value="28"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="CornerRadius" Value="14"/>
            <Setter Property="Background" Value="#1A30C9B0"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
        
        <!-- 功能图标激活样式 -->
        <Style Selector="Border.IconContainerActive">
            <Setter Property="Background" Value="#2A30C9B0"/>
        </Style>
        
        <!-- 箭头图标容器样式 -->
        <Style Selector="Border.ArrowContainer">
            <Setter Property="Width" Value="24"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Background" Value="#15FFFFFF"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,-6,0"/>
            <Setter Property="Child">
                <Setter.Value>
                    <Template>
                        <Canvas Width="24" Height="24">
                            <Path Classes="ArrowIcon" Canvas.Left="9" Canvas.Top="6"/>
                        </Canvas>
                    </Template>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 箭头图标基础样式 -->
        <Style Selector="Path.ArrowIcon">
            <Setter Property="Data" Value="M0,0 L6,6 L0,12"/>
            <Setter Property="Stroke" Value="#BBBBBB"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
            <Setter Property="StrokeLineCap" Value="Round"/>
            <Setter Property="Width" Value="6"/>
            <Setter Property="Height" Value="12"/>
        </Style>
        
        <!-- ====================== 按钮样式 ====================== -->
        
        <!-- 通用按钮样式 -->
        <Style Selector="Button">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Background" Value="#30C9B0"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Transitions">
                <Setter.Value>
                    <Transitions>
                        <TransformOperationsTransition Property="RenderTransform" Duration="0.1" Easing="CubicEaseOut"/>
                        <BrushTransition Property="Background" Duration="0.1" Easing="CubicEaseOut"/>
                    </Transitions>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransform" Value="scale(1.0)"/>
        </Style>
        
        <Style Selector="Button:pointerover">
            <Setter Property="Background" Value="#25A590"/>
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>
        
        <Style Selector="Button:pressed">
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
            <Setter Property="Background" Value="#1E8A75"/>
        </Style>
        
        <!-- 次要按钮样式 -->
        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="#25262B"/>
            <Setter Property="BorderBrush" Value="#40444A"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Background" Value="#303136"/>
            <Setter Property="BorderBrush" Value="#50555A"/>
        </Style>
        
        <Style Selector="Button.secondary:pressed">
            <Setter Property="Background" Value="#1E1F23"/>
        </Style>
        
        <!-- 标准按钮样式 -->
        <Style Selector="Button.StandardButton">
            <Setter Property="Background" Value="#2C2C2C"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style Selector="Button.StandardButton:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#3C3C3C"/>
            <Setter Property="TextBlock.Foreground" Value="{Binding $parent[Button].Foreground}"/>
        </Style>

        <Style Selector="Button.StandardButton:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="#444444"/>
            <Setter Property="TextBlock.Foreground" Value="{Binding $parent[Button].Foreground}"/>
        </Style>

        <Style Selector="Button.StandardButton:disabled /template/ ContentPresenter">
            <Setter Property="Background" Value="#1C1C1C"/>
            <Setter Property="TextBlock.Foreground" Value="#666666"/>
        </Style>
        
        <!-- 主按钮样式 -->
        <Style Selector="Button.PrimaryButton">
            <Setter Property="Background" Value="#30C9B0"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style Selector="Button.PrimaryButton:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#29B09B"/>
        </Style>

        <Style Selector="Button.PrimaryButton:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="#25A089"/>
        </Style>
        
        <!-- 处理按钮样式 - 处理中状态 -->
        <Style Selector="Button.ProcessingButton">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#E74C3C"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E74C3C"/>
            <Setter Property="Transitions">
                <Setter.Value>
                    <Transitions>
                        <BrushTransition Property="Foreground" Duration="0:0:0.2"/>
                        <BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
                    </Transitions>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style Selector="Button.ProcessingButton:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#20E74C3C"/>
            <Setter Property="BorderBrush" Value="#F05749"/>
            <Setter Property="TextBlock.Foreground" Value="#E74C3C"/>
        </Style>
        
        <!-- 处理按钮样式 - 待处理状态 -->
        <Style Selector="Button.ReadyButton">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#30C9B0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#30C9B0"/>
            <Setter Property="Transitions">
                <Setter.Value>
                    <Transitions>
                        <BrushTransition Property="Foreground" Duration="0:0:0.2"/>
                        <BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
                    </Transitions>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style Selector="Button.ReadyButton:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#2030C9B0"/>
            <Setter Property="BorderBrush" Value="#40C9B0"/>
            <Setter Property="TextBlock.Foreground" Value="#30C9B0"/>
        </Style>
        
        <!-- ====================== 卡片和边框样式 ====================== -->
        
        <!-- 统一的边框样式 -->
        <Style Selector="Border.UniformBorder">
            <Setter Property="Background" Value="#222222"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="BoxShadow" Value="0 5 30 5 #60000000"/>
        </Style>
        
        <!-- 统一的卡片样式 -->
        <Style Selector="Border.CardBorder">
            <Setter Property="Background" Value="#262626"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="BoxShadow" Value="0 1 4 0 #10000000"/>
        </Style>
        
        <!-- 下拉框全局样式 -->
        <Style Selector="ComboBox">
            <Setter Property="Background" Value="#2C2C2C"/>
            <Setter Property="BorderBrush" Value="#3A3A3A"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MaxDropDownHeight" Value="200"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Height" Value="28"/>
        </Style>
        
        <Style Selector="ComboBox:pointerover /template/ Border">
            <Setter Property="Background" Value="#3A3A3A"/>
            <Setter Property="BorderBrush" Value="#555555"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style Selector="ComboBoxItem">
            <Setter Property="Background" Value="#2C2C2C"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="TextBlock.TextWrapping" Value="NoWrap"/>
            <Setter Property="TextBlock.TextTrimming" Value="None"/>
        </Style>
        
        <Style Selector="ComboBoxItem:pointerover">
            <Setter Property="Background" Value="#333333"/>
        </Style>
        
        <Style Selector="ComboBoxItem:selected">
            <Setter Property="Background" Value="#333333"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <!-- 设置ComboBox下拉部分的样式 -->
        <Style Selector="ComboBox /template/ Popup">
            <Setter Property="MinWidth" Value="{Binding $parent[ComboBox].Bounds.Width}"/>
        </Style>
        
        <!-- ====================== 文本和输入控件样式 ====================== -->
        
        <!-- 文本框样式 -->
        <Style Selector="TextBox">
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="BorderBrush" Value="#40444A"/>
            <Setter Property="Background" Value="#25262B"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Padding" Value="10,0"/>
            <Setter Property="CaretBrush" Value="White"/>
            <Setter Property="SelectionBrush" Value="#30C9B0"/>
            <Setter Property="SelectionForegroundBrush" Value="White"/>
        </Style>
        
        <!-- 全局字体样式 -->
        <Style Selector="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <!-- 弹窗标题栏样式 -->
        <Style Selector="TextBlock.DialogTitle">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>
        
        <!-- ====================== 其他控件样式 ====================== -->
        
        <!-- 复选框样式 -->
        <Style Selector="CheckBox">
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="11"/>
        </Style>
        
        <Style Selector="CheckBox:checked /template/ Border#NormalRectangle">
            <Setter Property="Background" Value="#30C9B0"/>
            <Setter Property="BorderBrush" Value="#30C9B0"/>
        </Style>
        
        <Style Selector="CheckBox:pointerover /template/ Border#NormalRectangle">
            <Setter Property="BorderBrush" Value="#30C9B0"/>
        </Style>
        
        <!-- 此处移除了Toast样式，现在直接在代码中设置 -->
    </Application.Styles>
</Application>