<!DOCTYPE html>
<html>
<!-- Created by GNU Texinfo 7.0.1, https://www.gnu.org/software/texinfo/ -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FFmpeg Protocols Documentation</title>

<meta name="description" content="FFmpeg Protocols Documentation">
<meta name="keywords" content="FFmpeg Protocols Documentation">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta name="viewport" content="width=device-width,initial-scale=1">

<link href="#SEC_Contents" rel="contents" title="Table of Contents">
<style type="text/css">
<!--
div.example {margin-left: 3.2em}
ul.mark-bullet {list-style-type: disc}
ul.toc-numbered-mark {list-style: none}
-->
</style>


</head>

<body lang="en">


<div class="top-level-extent" id="SEC_Top">


<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Protocol-Options" href="#Protocol-Options">2 Protocol Options</a></li>
  <li><a id="toc-Protocols" href="#Protocols">3 Protocols</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-amqp" href="#amqp">3.1 amqp</a></li>
    <li><a id="toc-async" href="#async">3.2 async</a></li>
    <li><a id="toc-bluray" href="#bluray">3.3 bluray</a></li>
    <li><a id="toc-cache" href="#cache">3.4 cache</a></li>
    <li><a id="toc-concat" href="#concat">3.5 concat</a></li>
    <li><a id="toc-concatf" href="#concatf">3.6 concatf</a></li>
    <li><a id="toc-crypto" href="#crypto">3.7 crypto</a></li>
    <li><a id="toc-data" href="#data">3.8 data</a></li>
    <li><a id="toc-fd" href="#fd">3.9 fd</a></li>
    <li><a id="toc-file" href="#file">3.10 file</a></li>
    <li><a id="toc-ftp" href="#ftp">3.11 ftp</a></li>
    <li><a id="toc-gopher" href="#gopher">3.12 gopher</a></li>
    <li><a id="toc-gophers" href="#gophers">3.13 gophers</a></li>
    <li><a id="toc-hls" href="#hls">3.14 hls</a></li>
    <li><a id="toc-http" href="#http">3.15 http</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-HTTP-Cookies" href="#HTTP-Cookies">3.15.1 HTTP Cookies</a></li>
    </ul></li>
    <li><a id="toc-Icecast" href="#Icecast">3.16 Icecast</a></li>
    <li><a id="toc-ipfs" href="#ipfs">3.17 ipfs</a></li>
    <li><a id="toc-mmst" href="#mmst">3.18 mmst</a></li>
    <li><a id="toc-mmsh" href="#mmsh">3.19 mmsh</a></li>
    <li><a id="toc-md5" href="#md5">3.20 md5</a></li>
    <li><a id="toc-pipe" href="#pipe">3.21 pipe</a></li>
    <li><a id="toc-prompeg" href="#prompeg">3.22 prompeg</a></li>
    <li><a id="toc-rist" href="#rist">3.23 rist</a></li>
    <li><a id="toc-rtmp" href="#rtmp">3.24 rtmp</a></li>
    <li><a id="toc-rtmpe" href="#rtmpe">3.25 rtmpe</a></li>
    <li><a id="toc-rtmps" href="#rtmps">3.26 rtmps</a></li>
    <li><a id="toc-rtmpt" href="#rtmpt">3.27 rtmpt</a></li>
    <li><a id="toc-rtmpte" href="#rtmpte">3.28 rtmpte</a></li>
    <li><a id="toc-rtmpts" href="#rtmpts">3.29 rtmpts</a></li>
    <li><a id="toc-libsmbclient" href="#libsmbclient">3.30 libsmbclient</a></li>
    <li><a id="toc-libssh" href="#libssh">3.31 libssh</a></li>
    <li><a id="toc-librtmp-rtmp_002c-rtmpe_002c-rtmps_002c-rtmpt_002c-rtmpte" href="#librtmp-rtmp_002c-rtmpe_002c-rtmps_002c-rtmpt_002c-rtmpte">3.32 librtmp rtmp, rtmpe, rtmps, rtmpt, rtmpte</a></li>
    <li><a id="toc-rtp" href="#rtp">3.33 rtp</a></li>
    <li><a id="toc-rtsp" href="#rtsp">3.34 rtsp</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Muxer" href="#Muxer">3.34.1 Muxer</a></li>
      <li><a id="toc-Demuxer" href="#Demuxer">3.34.2 Demuxer</a></li>
      <li><a id="toc-Examples" href="#Examples">3.34.3 Examples</a></li>
    </ul></li>
    <li><a id="toc-sap" href="#sap">3.35 sap</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Muxer-1" href="#Muxer-1">3.35.1 Muxer</a></li>
      <li><a id="toc-Demuxer-1" href="#Demuxer-1">3.35.2 Demuxer</a></li>
    </ul></li>
    <li><a id="toc-sctp" href="#sctp">3.36 sctp</a></li>
    <li><a id="toc-srt" href="#srt">3.37 srt</a></li>
    <li><a id="toc-srtp" href="#srtp">3.38 srtp</a></li>
    <li><a id="toc-subfile" href="#subfile">3.39 subfile</a></li>
    <li><a id="toc-tee" href="#tee">3.40 tee</a></li>
    <li><a id="toc-tcp" href="#tcp">3.41 tcp</a></li>
    <li><a id="toc-tls" href="#tls">3.42 tls</a></li>
    <li><a id="toc-udp" href="#udp">3.43 udp</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-1" href="#Examples-1">3.43.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-unix" href="#unix">3.44 unix</a></li>
    <li><a id="toc-zmq" href="#zmq">3.45 zmq</a></li>
  </ul></li>
  <li><a id="toc-See-Also" href="#See-Also">4 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">5 Authors</a></li>
</ul>
</div>
</div>
<div class="chapter-level-extent" id="Description">
<h2 class="chapter">1 Description</h2>

<p>This document describes the input and output protocols provided by the
libavformat library.
</p>

</div>
<div class="chapter-level-extent" id="Protocol-Options">
<h2 class="chapter">2 Protocol Options</h2>

<p>The libavformat library provides some generic global options, which
can be set on all the protocols. In addition each protocol may support
so-called private options, which are specific for that component.
</p>
<p>Options may be set by specifying -<var class="var">option</var> <var class="var">value</var> in the
FFmpeg tools, or by setting the value explicitly in the
<code class="code">AVFormatContext</code> options or using the <samp class="file">libavutil/opt.h</samp> API
for programmatic use.
</p>
<p>The list of supported options follows:
</p>
<dl class="table">
<dt><samp class="option">protocol_whitelist <var class="var">list</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set a &quot;,&quot;-separated list of allowed protocols. &quot;ALL&quot; matches all protocols. Protocols
prefixed by &quot;-&quot; are disabled.
All protocols are allowed by default but protocols used by an another
protocol (nested protocols) are restricted to a per protocol subset.
</p></dd>
</dl>


</div>
<div class="chapter-level-extent" id="Protocols">
<h2 class="chapter">3 Protocols</h2>

<p>Protocols are configured elements in FFmpeg that enable access to
resources that require specific protocols.
</p>
<p>When you configure your FFmpeg build, all the supported protocols are
enabled by default. You can list all available ones using the
configure option &quot;&ndash;list-protocols&quot;.
</p>
<p>You can disable all the protocols using the configure option
&quot;&ndash;disable-protocols&quot;, and selectively enable a protocol using the
option &quot;&ndash;enable-protocol=<var class="var">PROTOCOL</var>&quot;, or you can disable a
particular protocol using the option
&quot;&ndash;disable-protocol=<var class="var">PROTOCOL</var>&quot;.
</p>
<p>The option &quot;-protocols&quot; of the ff* tools will display the list of
supported protocols.
</p>
<p>All protocols accept the following options:
</p>
<dl class="table">
<dt><samp class="option">rw_timeout</samp></dt>
<dd><p>Maximum time to wait for (network) read/write operations to complete,
in microseconds.
</p></dd>
</dl>

<p>A description of the currently available protocols follows.
</p>
<ul class="mini-toc">
<li><a href="#amqp" accesskey="1">amqp</a></li>
<li><a href="#async" accesskey="2">async</a></li>
<li><a href="#bluray" accesskey="3">bluray</a></li>
<li><a href="#cache" accesskey="4">cache</a></li>
<li><a href="#concat" accesskey="5">concat</a></li>
<li><a href="#concatf" accesskey="6">concatf</a></li>
<li><a href="#crypto" accesskey="7">crypto</a></li>
<li><a href="#data" accesskey="8">data</a></li>
<li><a href="#fd" accesskey="9">fd</a></li>
<li><a href="#file">file</a></li>
<li><a href="#ftp">ftp</a></li>
<li><a href="#gopher">gopher</a></li>
<li><a href="#gophers">gophers</a></li>
<li><a href="#hls">hls</a></li>
<li><a href="#http">http</a></li>
<li><a href="#Icecast">Icecast</a></li>
<li><a href="#ipfs">ipfs</a></li>
<li><a href="#mmst">mmst</a></li>
<li><a href="#mmsh">mmsh</a></li>
<li><a href="#md5">md5</a></li>
<li><a href="#pipe">pipe</a></li>
<li><a href="#prompeg">prompeg</a></li>
<li><a href="#rist">rist</a></li>
<li><a href="#rtmp">rtmp</a></li>
<li><a href="#rtmpe">rtmpe</a></li>
<li><a href="#rtmps">rtmps</a></li>
<li><a href="#rtmpt">rtmpt</a></li>
<li><a href="#rtmpte">rtmpte</a></li>
<li><a href="#rtmpts">rtmpts</a></li>
<li><a href="#libsmbclient">libsmbclient</a></li>
<li><a href="#libssh">libssh</a></li>
<li><a href="#librtmp-rtmp_002c-rtmpe_002c-rtmps_002c-rtmpt_002c-rtmpte">librtmp rtmp, rtmpe, rtmps, rtmpt, rtmpte</a></li>
<li><a href="#rtp">rtp</a></li>
<li><a href="#rtsp">rtsp</a></li>
<li><a href="#sap">sap</a></li>
<li><a href="#sctp">sctp</a></li>
<li><a href="#srt">srt</a></li>
<li><a href="#srtp">srtp</a></li>
<li><a href="#subfile">subfile</a></li>
<li><a href="#tee">tee</a></li>
<li><a href="#tcp">tcp</a></li>
<li><a href="#tls">tls</a></li>
<li><a href="#udp">udp</a></li>
<li><a href="#unix">unix</a></li>
<li><a href="#zmq">zmq</a></li>
</ul>
<div class="section-level-extent" id="amqp">
<h3 class="section">3.1 amqp</h3>

<p>Advanced Message Queueing Protocol (AMQP) version 0-9-1 is a broker based
publish-subscribe communication protocol.
</p>
<p>FFmpeg must be compiled with &ndash;enable-librabbitmq to support AMQP. A separate
AMQP broker must also be run. An example open-source AMQP broker is RabbitMQ.
</p>
<p>After starting the broker, an FFmpeg client may stream data to the broker using
the command:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i input -f mpegts amqp://[[user]:[password]@]hostname[:port][/vhost]
</pre></div>

<p>Where hostname and port (default is 5672) is the address of the broker. The
client may also set a user/password for authentication. The default for both
fields is &quot;guest&quot;. Name of virtual host on broker can be set with vhost. The
default value is &quot;/&quot;.
</p>
<p>Muliple subscribers may stream from the broker using the command:
</p><div class="example">
<pre class="example-preformatted">ffplay amqp://[[user]:[password]@]hostname[:port][/vhost]
</pre></div>

<p>In RabbitMQ all data published to the broker flows through a specific exchange,
and each subscribing client has an assigned queue/buffer. When a packet arrives
at an exchange, it may be copied to a client&rsquo;s queue depending on the exchange
and routing_key fields.
</p>
<p>The following options are supported:
</p>
<dl class="table">
<dt><samp class="option">exchange</samp></dt>
<dd><p>Sets the exchange to use on the broker. RabbitMQ has several predefined
exchanges: &quot;amq.direct&quot; is the default exchange, where the publisher and
subscriber must have a matching routing_key; &quot;amq.fanout&quot; is the same as a
broadcast operation (i.e. the data is forwarded to all queues on the fanout
exchange independent of the routing_key); and &quot;amq.topic&quot; is similar to
&quot;amq.direct&quot;, but allows for more complex pattern matching (refer to the RabbitMQ
documentation).
</p>
</dd>
<dt><samp class="option">routing_key</samp></dt>
<dd><p>Sets the routing key. The default value is &quot;amqp&quot;. The routing key is used on
the &quot;amq.direct&quot; and &quot;amq.topic&quot; exchanges to decide whether packets are written
to the queue of a subscriber.
</p>
</dd>
<dt><samp class="option">pkt_size</samp></dt>
<dd><p>Maximum size of each packet sent/received to the broker. Default is 131072.
Minimum is 4096 and max is any large value (representable by an int). When
receiving packets, this sets an internal buffer size in FFmpeg. It should be
equal to or greater than the size of the published packets to the broker. Otherwise
the received message may be truncated causing decoding errors.
</p>
</dd>
<dt><samp class="option">connection_timeout</samp></dt>
<dd><p>The timeout in seconds during the initial connection to the broker. The
default value is rw_timeout, or 5 seconds if rw_timeout is not set.
</p>
</dd>
<dt><samp class="option">delivery_mode <var class="var">mode</var></samp></dt>
<dd><p>Sets the delivery mode of each message sent to broker.
The following values are accepted:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">persistent</samp>&rsquo;</dt>
<dd><p>Delivery mode set to &quot;persistent&quot; (2). This is the default value.
Messages may be written to the broker&rsquo;s disk depending on its setup.
</p>
</dd>
<dt>&lsquo;<samp class="samp">non-persistent</samp>&rsquo;</dt>
<dd><p>Delivery mode set to &quot;non-persistent&quot; (1).
Messages will stay in broker&rsquo;s memory unless the broker is under memory
pressure.
</p>
</dd>
</dl>

</dd>
</dl>

</div>
<div class="section-level-extent" id="async">
<h3 class="section">3.2 async</h3>

<p>Asynchronous data filling wrapper for input stream.
</p>
<p>Fill data in a background thread, to decouple I/O operation from demux thread.
</p>
<div class="example">
<pre class="example-preformatted">async:<var class="var">URL</var>
async:http://host/resource
async:cache:http://host/resource
</pre></div>

</div>
<div class="section-level-extent" id="bluray">
<h3 class="section">3.3 bluray</h3>

<p>Read BluRay playlist.
</p>
<p>The accepted options are:
</p><dl class="table">
<dt><samp class="option">angle</samp></dt>
<dd><p>BluRay angle
</p>
</dd>
<dt><samp class="option">chapter</samp></dt>
<dd><p>Start chapter (1...N)
</p>
</dd>
<dt><samp class="option">playlist</samp></dt>
<dd><p>Playlist to read (BDMV/PLAYLIST/?????.mpls)
</p>
</dd>
</dl>

<p>Examples:
</p>
<p>Read longest playlist from BluRay mounted to /mnt/bluray:
</p><div class="example">
<pre class="example-preformatted">bluray:/mnt/bluray
</pre></div>

<p>Read angle 2 of playlist 4 from BluRay mounted to /mnt/bluray, start from chapter 2:
</p><div class="example">
<pre class="example-preformatted">-playlist 4 -angle 2 -chapter 2 bluray:/mnt/bluray
</pre></div>

</div>
<div class="section-level-extent" id="cache">
<h3 class="section">3.4 cache</h3>

<p>Caching wrapper for input stream.
</p>
<p>Cache the input stream to temporary file. It brings seeking capability to live streams.
</p>
<p>The accepted options are:
</p><dl class="table">
<dt><samp class="option">read_ahead_limit</samp></dt>
<dd><p>Amount in bytes that may be read ahead when seeking isn&rsquo;t supported. Range is -1 to INT_MAX.
-1 for unlimited. Default is 65536.
</p>
</dd>
</dl>

<p>URL Syntax is
</p><div class="example">
<pre class="example-preformatted">cache:<var class="var">URL</var>
</pre></div>

</div>
<div class="section-level-extent" id="concat">
<h3 class="section">3.5 concat</h3>

<p>Physical concatenation protocol.
</p>
<p>Read and seek from many resources in sequence as if they were
a unique resource.
</p>
<p>A URL accepted by this protocol has the syntax:
</p><div class="example">
<pre class="example-preformatted">concat:<var class="var">URL1</var>|<var class="var">URL2</var>|...|<var class="var">URLN</var>
</pre></div>

<p>where <var class="var">URL1</var>, <var class="var">URL2</var>, ..., <var class="var">URLN</var> are the urls of the
resource to be concatenated, each one possibly specifying a distinct
protocol.
</p>
<p>For example to read a sequence of files <samp class="file">split1.mpeg</samp>,
<samp class="file">split2.mpeg</samp>, <samp class="file">split3.mpeg</samp> with <code class="command">ffplay</code> use the
command:
</p><div class="example">
<pre class="example-preformatted">ffplay concat:split1.mpeg\|split2.mpeg\|split3.mpeg
</pre></div>

<p>Note that you may need to escape the character &quot;|&quot; which is special for
many shells.
</p>
</div>
<div class="section-level-extent" id="concatf">
<h3 class="section">3.6 concatf</h3>

<p>Physical concatenation protocol using a line break delimited list of
resources.
</p>
<p>Read and seek from many resources in sequence as if they were
a unique resource.
</p>
<p>A URL accepted by this protocol has the syntax:
</p><div class="example">
<pre class="example-preformatted">concatf:<var class="var">URL</var>
</pre></div>

<p>where <var class="var">URL</var> is the url containing a line break delimited list of
resources to be concatenated, each one possibly specifying a distinct
protocol. Special characters must be escaped with backslash or single
quotes. See <a data-manual="ffmpeg-utils" href="ffmpeg-utils.html#quoting_005fand_005fescaping">(ffmpeg-utils)the &quot;Quoting and escaping&quot;
section in the ffmpeg-utils(1) manual</a>.
</p>
<p>For example to read a sequence of files <samp class="file">split1.mpeg</samp>,
<samp class="file">split2.mpeg</samp>, <samp class="file">split3.mpeg</samp> listed in separate lines within
a file <samp class="file">split.txt</samp> with <code class="command">ffplay</code> use the command:
</p><div class="example">
<pre class="example-preformatted">ffplay concatf:split.txt
</pre></div>
<p>Where <samp class="file">split.txt</samp> contains the lines:
</p><div class="example">
<pre class="example-preformatted">split1.mpeg
split2.mpeg
split3.mpeg
</pre></div>

</div>
<div class="section-level-extent" id="crypto">
<h3 class="section">3.7 crypto</h3>

<p>AES-encrypted stream reading protocol.
</p>
<p>The accepted options are:
</p><dl class="table">
<dt><samp class="option">key</samp></dt>
<dd><p>Set the AES decryption key binary block from given hexadecimal representation.
</p>
</dd>
<dt><samp class="option">iv</samp></dt>
<dd><p>Set the AES decryption initialization vector binary block from given hexadecimal representation.
</p></dd>
</dl>

<p>Accepted URL formats:
</p><div class="example">
<pre class="example-preformatted">crypto:<var class="var">URL</var>
crypto+<var class="var">URL</var>
</pre></div>

</div>
<div class="section-level-extent" id="data">
<h3 class="section">3.8 data</h3>

<p>Data in-line in the URI. See <a class="url" href="http://en.wikipedia.org/wiki/Data_URI_scheme">http://en.wikipedia.org/wiki/Data_URI_scheme</a>.
</p>
<p>For example, to convert a GIF file given inline with <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i &quot;data:image/gif;base64,R0lGODdhCAAIAMIEAAAAAAAA//8AAP//AP///////////////ywAAAAACAAIAAADF0gEDLojDgdGiJdJqUX02iB4E8Q9jUMkADs=&quot; smiley.png
</pre></div>

</div>
<div class="section-level-extent" id="fd">
<h3 class="section">3.9 fd</h3>

<p>File descriptor access protocol.
</p>
<p>The accepted syntax is:
</p><div class="example">
<pre class="example-preformatted">fd: -fd <var class="var">file_descriptor</var>
</pre></div>

<p>If <samp class="option">fd</samp> is not specified, by default the stdout file descriptor will be
used for writing, stdin for reading. Unlike the pipe protocol, fd protocol has
seek support if it corresponding to a regular file. fd protocol doesn&rsquo;t support
pass file descriptor via URL for security.
</p>
<p>This protocol accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">blocksize</samp></dt>
<dd><p>Set I/O operation maximum block size, in bytes. Default value is
<code class="code">INT_MAX</code>, which results in not limiting the requested block size.
Setting this value reasonably low improves user termination request reaction
time, which is valuable if data transmission is slow.
</p>
</dd>
<dt><samp class="option">fd</samp></dt>
<dd><p>Set file descriptor.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="file">
<h3 class="section">3.10 file</h3>

<p>File access protocol.
</p>
<p>Read from or write to a file.
</p>
<p>A file URL can have the form:
</p><div class="example">
<pre class="example-preformatted">file:<var class="var">filename</var>
</pre></div>

<p>where <var class="var">filename</var> is the path of the file to read.
</p>
<p>An URL that does not have a protocol prefix will be assumed to be a
file URL. Depending on the build, an URL that looks like a Windows
path with the drive letter at the beginning will also be assumed to be
a file URL (usually not the case in builds for unix-like systems).
</p>
<p>For example to read from a file <samp class="file">input.mpeg</samp> with <code class="command">ffmpeg</code>
use the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i file:input.mpeg output.mpeg
</pre></div>

<p>This protocol accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">truncate</samp></dt>
<dd><p>Truncate existing files on write, if set to 1. A value of 0 prevents
truncating. Default value is 1.
</p>
</dd>
<dt><samp class="option">blocksize</samp></dt>
<dd><p>Set I/O operation maximum block size, in bytes. Default value is
<code class="code">INT_MAX</code>, which results in not limiting the requested block size.
Setting this value reasonably low improves user termination request reaction
time, which is valuable for files on slow medium.
</p>
</dd>
<dt><samp class="option">follow</samp></dt>
<dd><p>If set to 1, the protocol will retry reading at the end of the file, allowing
reading files that still are being written. In order for this to terminate,
you either need to use the rw_timeout option, or use the interrupt callback
(for API users).
</p>
</dd>
<dt><samp class="option">seekable</samp></dt>
<dd><p>Controls if seekability is advertised on the file. 0 means non-seekable, -1
means auto (seekable for normal files, non-seekable for named pipes).
</p>
<p>Many demuxers handle seekable and non-seekable resources differently,
overriding this might speed up opening certain files at the cost of losing some
features (e.g. accurate seeking).
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="ftp">
<h3 class="section">3.11 ftp</h3>

<p>FTP (File Transfer Protocol).
</p>
<p>Read from or write to remote resources using FTP protocol.
</p>
<p>Following syntax is required.
</p><div class="example">
<pre class="example-preformatted">ftp://[user[:password]@]server[:port]/path/to/remote/resource.mpeg
</pre></div>

<p>This protocol accepts the following options.
</p>
<dl class="table">
<dt><samp class="option">timeout</samp></dt>
<dd><p>Set timeout in microseconds of socket I/O operations used by the underlying low level
operation. By default it is set to -1, which means that the timeout is
not specified.
</p>
</dd>
<dt><samp class="option">ftp-user</samp></dt>
<dd><p>Set a user to be used for authenticating to the FTP server. This is overridden by the
user in the FTP URL.
</p>
</dd>
<dt><samp class="option">ftp-password</samp></dt>
<dd><p>Set a password to be used for authenticating to the FTP server. This is overridden by
the password in the FTP URL, or by <samp class="option">ftp-anonymous-password</samp> if no user is set.
</p>
</dd>
<dt><samp class="option">ftp-anonymous-password</samp></dt>
<dd><p>Password used when login as anonymous user. Typically an e-mail address
should be used.
</p>
</dd>
<dt><samp class="option">ftp-write-seekable</samp></dt>
<dd><p>Control seekability of connection during encoding. If set to 1 the
resource is supposed to be seekable, if set to 0 it is assumed not
to be seekable. Default value is 0.
</p></dd>
</dl>

<p>NOTE: Protocol can be used as output, but it is recommended to not do
it, unless special care is taken (tests, customized server configuration
etc.). Different FTP servers behave in different way during seek
operation. ff* tools may produce incomplete content due to server limitations.
</p>
</div>
<div class="section-level-extent" id="gopher">
<h3 class="section">3.12 gopher</h3>

<p>Gopher protocol.
</p>
</div>
<div class="section-level-extent" id="gophers">
<h3 class="section">3.13 gophers</h3>

<p>Gophers protocol.
</p>
<p>The Gopher protocol with TLS encapsulation.
</p>
</div>
<div class="section-level-extent" id="hls">
<h3 class="section">3.14 hls</h3>

<p>Read Apple HTTP Live Streaming compliant segmented stream as
a uniform one. The M3U8 playlists describing the segments can be
remote HTTP resources or local files, accessed using the standard
file protocol.
The nested protocol is declared by specifying
&quot;+<var class="var">proto</var>&quot; after the hls URI scheme name, where <var class="var">proto</var>
is either &quot;file&quot; or &quot;http&quot;.
</p>
<div class="example">
<pre class="example-preformatted">hls+http://host/path/to/remote/resource.m3u8
hls+file://path/to/local/resource.m3u8
</pre></div>

<p>Using this protocol is discouraged - the hls demuxer should work
just as well (if not, please report the issues) and is more complete.
To use the hls demuxer instead, simply use the direct URLs to the
m3u8 files.
</p>
</div>
<div class="section-level-extent" id="http">
<h3 class="section">3.15 http</h3>

<p>HTTP (Hyper Text Transfer Protocol).
</p>
<p>This protocol accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">seekable</samp></dt>
<dd><p>Control seekability of connection. If set to 1 the resource is
supposed to be seekable, if set to 0 it is assumed not to be seekable,
if set to -1 it will try to autodetect if it is seekable. Default
value is -1.
</p>
</dd>
<dt><samp class="option">chunked_post</samp></dt>
<dd><p>If set to 1 use chunked Transfer-Encoding for posts, default is 1.
</p>
</dd>
<dt><samp class="option">content_type</samp></dt>
<dd><p>Set a specific content type for the POST messages or for listen mode.
</p>
</dd>
<dt><samp class="option">http_proxy</samp></dt>
<dd><p>set HTTP proxy to tunnel through e.g. http://example.com:1234
</p>
</dd>
<dt><samp class="option">headers</samp></dt>
<dd><p>Set custom HTTP headers, can override built in default headers. The
value must be a string encoding the headers.
</p>
</dd>
<dt><samp class="option">multiple_requests</samp></dt>
<dd><p>Use persistent connections if set to 1, default is 0.
</p>
</dd>
<dt><samp class="option">post_data</samp></dt>
<dd><p>Set custom HTTP post data.
</p>
</dd>
<dt><samp class="option">referer</samp></dt>
<dd><p>Set the Referer header. Include &rsquo;Referer: URL&rsquo; header in HTTP request.
</p>
</dd>
<dt><samp class="option">user_agent</samp></dt>
<dd><p>Override the User-Agent header. If not specified the protocol will use a
string describing the libavformat build. (&quot;Lavf/&lt;version&gt;&quot;)
</p>
</dd>
<dt><samp class="option">reconnect_at_eof</samp></dt>
<dd><p>If set then eof is treated like an error and causes reconnection, this is useful
for live / endless streams.
</p>
</dd>
<dt><samp class="option">reconnect_streamed</samp></dt>
<dd><p>If set then even streamed/non seekable streams will be reconnected on errors.
</p>
</dd>
<dt><samp class="option">reconnect_on_network_error</samp></dt>
<dd><p>Reconnect automatically in case of TCP/TLS errors during connect.
</p>
</dd>
<dt><samp class="option">reconnect_on_http_error</samp></dt>
<dd><p>A comma separated list of HTTP status codes to reconnect on. The list can
include specific status codes (e.g. &rsquo;503&rsquo;) or the strings &rsquo;4xx&rsquo; / &rsquo;5xx&rsquo;.
</p>
</dd>
<dt><samp class="option">reconnect_delay_max</samp></dt>
<dd><p>Sets the maximum delay in seconds after which to give up reconnecting
</p>
</dd>
<dt><samp class="option">mime_type</samp></dt>
<dd><p>Export the MIME type.
</p>
</dd>
<dt><samp class="option">http_version</samp></dt>
<dd><p>Exports the HTTP response version number. Usually &quot;1.0&quot; or &quot;1.1&quot;.
</p>
</dd>
<dt><samp class="option">icy</samp></dt>
<dd><p>If set to 1 request ICY (SHOUTcast) metadata from the server. If the server
supports this, the metadata has to be retrieved by the application by reading
the <samp class="option">icy_metadata_headers</samp> and <samp class="option">icy_metadata_packet</samp> options.
The default is 1.
</p>
</dd>
<dt><samp class="option">icy_metadata_headers</samp></dt>
<dd><p>If the server supports ICY metadata, this contains the ICY-specific HTTP reply
headers, separated by newline characters.
</p>
</dd>
<dt><samp class="option">icy_metadata_packet</samp></dt>
<dd><p>If the server supports ICY metadata, and <samp class="option">icy</samp> was set to 1, this
contains the last non-empty metadata packet sent by the server. It should be
polled in regular intervals by applications interested in mid-stream metadata
updates.
</p>
</dd>
<dt><samp class="option">cookies</samp></dt>
<dd><p>Set the cookies to be sent in future requests. The format of each cookie is the
same as the value of a Set-Cookie HTTP response field. Multiple cookies can be
delimited by a newline character.
</p>
</dd>
<dt><samp class="option">offset</samp></dt>
<dd><p>Set initial byte offset.
</p>
</dd>
<dt><samp class="option">end_offset</samp></dt>
<dd><p>Try to limit the request to bytes preceding this offset.
</p>
</dd>
<dt><samp class="option">method</samp></dt>
<dd><p>When used as a client option it sets the HTTP method for the request.
</p>
<p>When used as a server option it sets the HTTP method that is going to be
expected from the client(s).
If the expected and the received HTTP method do not match the client will
be given a Bad Request response.
When unset the HTTP method is not checked for now. This will be replaced by
autodetection in the future.
</p>
</dd>
<dt><samp class="option">listen</samp></dt>
<dd><p>If set to 1 enables experimental HTTP server. This can be used to send data when
used as an output option, or read data from a client with HTTP POST when used as
an input option.
If set to 2 enables experimental multi-client HTTP server. This is not yet implemented
in ffmpeg.c and thus must not be used as a command line option.
</p><div class="example">
<pre class="example-preformatted"># Server side (sending):
ffmpeg -i somefile.ogg -c copy -listen 1 -f ogg http://<var class="var">server</var>:<var class="var">port</var>

# Client side (receiving):
ffmpeg -i http://<var class="var">server</var>:<var class="var">port</var> -c copy somefile.ogg

# Client can also be done with wget:
wget http://<var class="var">server</var>:<var class="var">port</var> -O somefile.ogg

# Server side (receiving):
ffmpeg -listen 1 -i http://<var class="var">server</var>:<var class="var">port</var> -c copy somefile.ogg

# Client side (sending):
ffmpeg -i somefile.ogg -chunked_post 0 -c copy -f ogg http://<var class="var">server</var>:<var class="var">port</var>

# Client can also be done with wget:
wget --post-file=somefile.ogg http://<var class="var">server</var>:<var class="var">port</var>
</pre></div>

</dd>
<dt><samp class="option">send_expect_100</samp></dt>
<dd><p>Send an Expect: 100-continue header for POST. If set to 1 it will send, if set
to 0 it won&rsquo;t, if set to -1 it will try to send if it is applicable. Default
value is -1.
</p>
</dd>
<dt><samp class="option">auth_type</samp></dt>
<dd>
<p>Set HTTP authentication type. No option for Digest, since this method requires
getting nonce parameters from the server first and can&rsquo;t be used straight away like
Basic.
</p>
<dl class="table">
<dt><samp class="option">none</samp></dt>
<dd><p>Choose the HTTP authentication type automatically. This is the default.
</p></dd>
<dt><samp class="option">basic</samp></dt>
<dd>
<p>Choose the HTTP basic authentication.
</p>
<p>Basic authentication sends a Base64-encoded string that contains a user name and password
for the client. Base64 is not a form of encryption and should be considered the same as
sending the user name and password in clear text (Base64 is a reversible encoding).
If a resource needs to be protected, strongly consider using an authentication scheme
other than basic authentication. HTTPS/TLS should be used with basic authentication.
Without these additional security enhancements, basic authentication should not be used
to protect sensitive or valuable information.
</p></dd>
</dl>

</dd>
</dl>

<ul class="mini-toc">
<li><a href="#HTTP-Cookies" accesskey="1">HTTP Cookies</a></li>
</ul>
<div class="subsection-level-extent" id="HTTP-Cookies">
<h4 class="subsection">3.15.1 HTTP Cookies</h4>

<p>Some HTTP requests will be denied unless cookie values are passed in with the
request. The <samp class="option">cookies</samp> option allows these cookies to be specified. At
the very least, each cookie must specify a value along with a path and domain.
HTTP requests that match both the domain and path will automatically include the
cookie value in the HTTP Cookie header field. Multiple cookies can be delimited
by a newline.
</p>
<p>The required syntax to play a stream specifying a cookie is:
</p><div class="example">
<pre class="example-preformatted">ffplay -cookies &quot;nlqptid=nltid=tsn; path=/; domain=somedomain.com;&quot; http://somedomain.com/somestream.m3u8
</pre></div>

</div>
</div>
<div class="section-level-extent" id="Icecast">
<h3 class="section">3.16 Icecast</h3>

<p>Icecast protocol (stream to Icecast servers)
</p>
<p>This protocol accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">ice_genre</samp></dt>
<dd><p>Set the stream genre.
</p>
</dd>
<dt><samp class="option">ice_name</samp></dt>
<dd><p>Set the stream name.
</p>
</dd>
<dt><samp class="option">ice_description</samp></dt>
<dd><p>Set the stream description.
</p>
</dd>
<dt><samp class="option">ice_url</samp></dt>
<dd><p>Set the stream website URL.
</p>
</dd>
<dt><samp class="option">ice_public</samp></dt>
<dd><p>Set if the stream should be public.
The default is 0 (not public).
</p>
</dd>
<dt><samp class="option">user_agent</samp></dt>
<dd><p>Override the User-Agent header. If not specified a string of the form
&quot;Lavf/&lt;version&gt;&quot; will be used.
</p>
</dd>
<dt><samp class="option">password</samp></dt>
<dd><p>Set the Icecast mountpoint password.
</p>
</dd>
<dt><samp class="option">content_type</samp></dt>
<dd><p>Set the stream content type. This must be set if it is different from
audio/mpeg.
</p>
</dd>
<dt><samp class="option">legacy_icecast</samp></dt>
<dd><p>This enables support for Icecast versions &lt; 2.4.0, that do not support the
HTTP PUT method but the SOURCE method.
</p>
</dd>
<dt><samp class="option">tls</samp></dt>
<dd><p>Establish a TLS (HTTPS) connection to Icecast.
</p>
</dd>
</dl>

<div class="example">
<pre class="example-preformatted">icecast://[<var class="var">username</var>[:<var class="var">password</var>]@]<var class="var">server</var>:<var class="var">port</var>/<var class="var">mountpoint</var>
</pre></div>

</div>
<div class="section-level-extent" id="ipfs">
<h3 class="section">3.17 ipfs</h3>

<p>InterPlanetary File System (IPFS) protocol support. One can access files stored
on the IPFS network through so-called gateways. These are http(s) endpoints.
This protocol wraps the IPFS native protocols (ipfs:// and ipns://) to be sent
to such a gateway. Users can (and should) host their own node which means this
protocol will use one&rsquo;s local gateway to access files on the IPFS network.
</p>
<p>This protocol accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">gateway</samp></dt>
<dd><p>Defines the gateway to use. When not set, the protocol will first try
locating the local gateway by looking at <code class="code">$IPFS_GATEWAY</code>, <code class="code">$IPFS_PATH</code>
and <code class="code">$HOME/.ipfs/</code>, in that order.
</p>
</dd>
</dl>

<p>One can use this protocol in 2 ways. Using IPFS:
</p><div class="example">
<pre class="example-preformatted">ffplay ipfs://&lt;hash&gt;
</pre></div>

<p>Or the IPNS protocol (IPNS is mutable IPFS):
</p><div class="example">
<pre class="example-preformatted">ffplay ipns://&lt;hash&gt;
</pre></div>

</div>
<div class="section-level-extent" id="mmst">
<h3 class="section">3.18 mmst</h3>

<p>MMS (Microsoft Media Server) protocol over TCP.
</p>
</div>
<div class="section-level-extent" id="mmsh">
<h3 class="section">3.19 mmsh</h3>

<p>MMS (Microsoft Media Server) protocol over HTTP.
</p>
<p>The required syntax is:
</p><div class="example">
<pre class="example-preformatted">mmsh://<var class="var">server</var>[:<var class="var">port</var>][/<var class="var">app</var>][/<var class="var">playpath</var>]
</pre></div>

</div>
<div class="section-level-extent" id="md5">
<h3 class="section">3.20 md5</h3>

<p>MD5 output protocol.
</p>
<p>Computes the MD5 hash of the data to be written, and on close writes
this to the designated output or stdout if none is specified. It can
be used to test muxers without writing an actual file.
</p>
<p>Some examples follow.
</p><div class="example">
<pre class="example-preformatted"># Write the MD5 hash of the encoded AVI file to the file output.avi.md5.
ffmpeg -i input.flv -f avi -y md5:output.avi.md5

# Write the MD5 hash of the encoded AVI file to stdout.
ffmpeg -i input.flv -f avi -y md5:
</pre></div>

<p>Note that some formats (typically MOV) require the output protocol to
be seekable, so they will fail with the MD5 output protocol.
</p>
</div>
<div class="section-level-extent" id="pipe">
<h3 class="section">3.21 pipe</h3>

<p>UNIX pipe access protocol.
</p>
<p>Read and write from UNIX pipes.
</p>
<p>The accepted syntax is:
</p><div class="example">
<pre class="example-preformatted">pipe:[<var class="var">number</var>]
</pre></div>

<p>If <samp class="option">fd</samp> isn&rsquo;t specified, <var class="var">number</var> is the number corresponding to the file descriptor of the
pipe (e.g. 0 for stdin, 1 for stdout, 2 for stderr).  If <var class="var">number</var>
is not specified, by default the stdout file descriptor will be used
for writing, stdin for reading.
</p>
<p>For example to read from stdin with <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">cat test.wav | ffmpeg -i pipe:0
# ...this is the same as...
cat test.wav | ffmpeg -i pipe:
</pre></div>

<p>For writing to stdout with <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i test.wav -f avi pipe:1 | cat &gt; test.avi
# ...this is the same as...
ffmpeg -i test.wav -f avi pipe: | cat &gt; test.avi
</pre></div>

<p>This protocol accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">blocksize</samp></dt>
<dd><p>Set I/O operation maximum block size, in bytes. Default value is
<code class="code">INT_MAX</code>, which results in not limiting the requested block size.
Setting this value reasonably low improves user termination request reaction
time, which is valuable if data transmission is slow.
</p></dd>
<dt><samp class="option">fd</samp></dt>
<dd><p>Set file descriptor.
</p></dd>
</dl>

<p>Note that some formats (typically MOV), require the output protocol to
be seekable, so they will fail with the pipe output protocol.
</p>
</div>
<div class="section-level-extent" id="prompeg">
<h3 class="section">3.22 prompeg</h3>

<p>Pro-MPEG Code of Practice #3 Release 2 FEC protocol.
</p>
<p>The Pro-MPEG CoP#3 FEC is a 2D parity-check forward error correction mechanism
for MPEG-2 Transport Streams sent over RTP.
</p>
<p>This protocol must be used in conjunction with the <code class="code">rtp_mpegts</code> muxer and
the <code class="code">rtp</code> protocol.
</p>
<p>The required syntax is:
</p><div class="example">
<pre class="example-preformatted">-f rtp_mpegts -fec prompeg=<var class="var">option</var>=<var class="var">val</var>... rtp://<var class="var">hostname</var>:<var class="var">port</var>
</pre></div>

<p>The destination UDP ports are <code class="code">port + 2</code> for the column FEC stream
and <code class="code">port + 4</code> for the row FEC stream.
</p>
<p>This protocol accepts the following options:
</p><dl class="table">
<dt><samp class="option">l=<var class="var">n</var></samp></dt>
<dd><p>The number of columns (4-20, LxD &lt;= 100)
</p>
</dd>
<dt><samp class="option">d=<var class="var">n</var></samp></dt>
<dd><p>The number of rows (4-20, LxD &lt;= 100)
</p>
</dd>
</dl>

<p>Example usage:
</p>
<div class="example">
<pre class="example-preformatted">-f rtp_mpegts -fec prompeg=l=8:d=4 rtp://<var class="var">hostname</var>:<var class="var">port</var>
</pre></div>

</div>
<div class="section-level-extent" id="rist">
<h3 class="section">3.23 rist</h3>

<p>Reliable Internet Streaming Transport protocol
</p>
<p>The accepted options are:
</p><dl class="table">
<dt><samp class="option">rist_profile</samp></dt>
<dd><p>Supported values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">simple</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dd><p>This one is default.
</p></dd>
<dt>&lsquo;<samp class="samp">advanced</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">buffer_size</samp></dt>
<dd><p>Set internal RIST buffer size in milliseconds for retransmission of data.
Default value is 0 which means the librist default (1 sec). Maximum value is 30
seconds.
</p>
</dd>
<dt><samp class="option">fifo_size</samp></dt>
<dd><p>Size of the librist receiver output fifo in number of packets. This must be a
power of 2.
Defaults to 8192 (vs the librist default of 1024).
</p>
</dd>
<dt><samp class="option">overrun_nonfatal=<var class="var">1|0</var></samp></dt>
<dd><p>Survive in case of librist fifo buffer overrun. Default value is 0.
</p>
</dd>
<dt><samp class="option">pkt_size</samp></dt>
<dd><p>Set maximum packet size for sending data. 1316 by default.
</p>
</dd>
<dt><samp class="option">log_level</samp></dt>
<dd><p>Set loglevel for RIST logging messages. You only need to set this if you
explicitly want to enable debug level messages or packet loss simulation,
otherwise the regular loglevel is respected.
</p>
</dd>
<dt><samp class="option">secret</samp></dt>
<dd><p>Set override of encryption secret, by default is unset.
</p>
</dd>
<dt><samp class="option">encryption</samp></dt>
<dd><p>Set encryption type, by default is disabled.
Acceptable values are 128 and 256.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="rtmp">
<h3 class="section">3.24 rtmp</h3>

<p>Real-Time Messaging Protocol.
</p>
<p>The Real-Time Messaging Protocol (RTMP) is used for streaming multimedia
content across a TCP/IP network.
</p>
<p>The required syntax is:
</p><div class="example">
<pre class="example-preformatted">rtmp://[<var class="var">username</var>:<var class="var">password</var>@]<var class="var">server</var>[:<var class="var">port</var>][/<var class="var">app</var>][/<var class="var">instance</var>][/<var class="var">playpath</var>]
</pre></div>

<p>The accepted parameters are:
</p><dl class="table">
<dt><samp class="option">username</samp></dt>
<dd><p>An optional username (mostly for publishing).
</p>
</dd>
<dt><samp class="option">password</samp></dt>
<dd><p>An optional password (mostly for publishing).
</p>
</dd>
<dt><samp class="option">server</samp></dt>
<dd><p>The address of the RTMP server.
</p>
</dd>
<dt><samp class="option">port</samp></dt>
<dd><p>The number of the TCP port to use (by default is 1935).
</p>
</dd>
<dt><samp class="option">app</samp></dt>
<dd><p>It is the name of the application to access. It usually corresponds to
the path where the application is installed on the RTMP server
(e.g. <samp class="file">/ondemand/</samp>, <samp class="file">/flash/live/</samp>, etc.). You can override
the value parsed from the URI through the <code class="code">rtmp_app</code> option, too.
</p>
</dd>
<dt><samp class="option">playpath</samp></dt>
<dd><p>It is the path or name of the resource to play with reference to the
application specified in <var class="var">app</var>, may be prefixed by &quot;mp4:&quot;. You
can override the value parsed from the URI through the <code class="code">rtmp_playpath</code>
option, too.
</p>
</dd>
<dt><samp class="option">listen</samp></dt>
<dd><p>Act as a server, listening for an incoming connection.
</p>
</dd>
<dt><samp class="option">timeout</samp></dt>
<dd><p>Maximum time to wait for the incoming connection. Implies listen.
</p></dd>
</dl>

<p>Additionally, the following parameters can be set via command line options
(or in code via <code class="code">AVOption</code>s):
</p><dl class="table">
<dt><samp class="option">rtmp_app</samp></dt>
<dd><p>Name of application to connect on the RTMP server. This option
overrides the parameter specified in the URI.
</p>
</dd>
<dt><samp class="option">rtmp_buffer</samp></dt>
<dd><p>Set the client buffer time in milliseconds. The default is 3000.
</p>
</dd>
<dt><samp class="option">rtmp_conn</samp></dt>
<dd><p>Extra arbitrary AMF connection parameters, parsed from a string,
e.g. like <code class="code">B:1 S:authMe O:1 NN:code:1.23 NS:flag:ok O:0</code>.
Each value is prefixed by a single character denoting the type,
B for Boolean, N for number, S for string, O for object, or Z for null,
followed by a colon. For Booleans the data must be either 0 or 1 for
FALSE or TRUE, respectively.  Likewise for Objects the data must be 0 or
1 to end or begin an object, respectively. Data items in subobjects may
be named, by prefixing the type with &rsquo;N&rsquo; and specifying the name before
the value (i.e. <code class="code">NB:myFlag:1</code>). This option may be used multiple
times to construct arbitrary AMF sequences.
</p>
</dd>
<dt><samp class="option">rtmp_flashver</samp></dt>
<dd><p>Version of the Flash plugin used to run the SWF player. The default
is LNX 9,0,124,2. (When publishing, the default is FMLE/3.0 (compatible;
&lt;libavformat version&gt;).)
</p>
</dd>
<dt><samp class="option">rtmp_flush_interval</samp></dt>
<dd><p>Number of packets flushed in the same request (RTMPT only). The default
is 10.
</p>
</dd>
<dt><samp class="option">rtmp_live</samp></dt>
<dd><p>Specify that the media is a live stream. No resuming or seeking in
live streams is possible. The default value is <code class="code">any</code>, which means the
subscriber first tries to play the live stream specified in the
playpath. If a live stream of that name is not found, it plays the
recorded stream. The other possible values are <code class="code">live</code> and
<code class="code">recorded</code>.
</p>
</dd>
<dt><samp class="option">rtmp_pageurl</samp></dt>
<dd><p>URL of the web page in which the media was embedded. By default no
value will be sent.
</p>
</dd>
<dt><samp class="option">rtmp_playpath</samp></dt>
<dd><p>Stream identifier to play or to publish. This option overrides the
parameter specified in the URI.
</p>
</dd>
<dt><samp class="option">rtmp_subscribe</samp></dt>
<dd><p>Name of live stream to subscribe to. By default no value will be sent.
It is only sent if the option is specified or if rtmp_live
is set to live.
</p>
</dd>
<dt><samp class="option">rtmp_swfhash</samp></dt>
<dd><p>SHA256 hash of the decompressed SWF file (32 bytes).
</p>
</dd>
<dt><samp class="option">rtmp_swfsize</samp></dt>
<dd><p>Size of the decompressed SWF file, required for SWFVerification.
</p>
</dd>
<dt><samp class="option">rtmp_swfurl</samp></dt>
<dd><p>URL of the SWF player for the media. By default no value will be sent.
</p>
</dd>
<dt><samp class="option">rtmp_swfverify</samp></dt>
<dd><p>URL to player swf file, compute hash/size automatically.
</p>
</dd>
<dt><samp class="option">rtmp_tcurl</samp></dt>
<dd><p>URL of the target stream. Defaults to proto://host[:port]/app.
</p>
</dd>
<dt><samp class="option">tcp_nodelay=<var class="var">1|0</var></samp></dt>
<dd><p>Set TCP_NODELAY to disable Nagle&rsquo;s algorithm. Default value is 0.
</p>
<p><em class="emph">Remark: Writing to the socket is currently not optimized to minimize system calls and reduces the efficiency / effect of TCP_NODELAY.</em>
</p>
</dd>
</dl>

<p>For example to read with <code class="command">ffplay</code> a multimedia resource named
&quot;sample&quot; from the application &quot;vod&quot; from an RTMP server &quot;myserver&quot;:
</p><div class="example">
<pre class="example-preformatted">ffplay rtmp://myserver/vod/sample
</pre></div>

<p>To publish to a password protected server, passing the playpath and
app names separately:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i &lt;input&gt; -f flv -rtmp_playpath some/long/path -rtmp_app long/app/name rtmp://username:password@myserver/
</pre></div>

</div>
<div class="section-level-extent" id="rtmpe">
<h3 class="section">3.25 rtmpe</h3>

<p>Encrypted Real-Time Messaging Protocol.
</p>
<p>The Encrypted Real-Time Messaging Protocol (RTMPE) is used for
streaming multimedia content within standard cryptographic primitives,
consisting of Diffie-Hellman key exchange and HMACSHA256, generating
a pair of RC4 keys.
</p>
</div>
<div class="section-level-extent" id="rtmps">
<h3 class="section">3.26 rtmps</h3>

<p>Real-Time Messaging Protocol over a secure SSL connection.
</p>
<p>The Real-Time Messaging Protocol (RTMPS) is used for streaming
multimedia content across an encrypted connection.
</p>
</div>
<div class="section-level-extent" id="rtmpt">
<h3 class="section">3.27 rtmpt</h3>

<p>Real-Time Messaging Protocol tunneled through HTTP.
</p>
<p>The Real-Time Messaging Protocol tunneled through HTTP (RTMPT) is used
for streaming multimedia content within HTTP requests to traverse
firewalls.
</p>
</div>
<div class="section-level-extent" id="rtmpte">
<h3 class="section">3.28 rtmpte</h3>

<p>Encrypted Real-Time Messaging Protocol tunneled through HTTP.
</p>
<p>The Encrypted Real-Time Messaging Protocol tunneled through HTTP (RTMPTE)
is used for streaming multimedia content within HTTP requests to traverse
firewalls.
</p>
</div>
<div class="section-level-extent" id="rtmpts">
<h3 class="section">3.29 rtmpts</h3>

<p>Real-Time Messaging Protocol tunneled through HTTPS.
</p>
<p>The Real-Time Messaging Protocol tunneled through HTTPS (RTMPTS) is used
for streaming multimedia content within HTTPS requests to traverse
firewalls.
</p>
</div>
<div class="section-level-extent" id="libsmbclient">
<h3 class="section">3.30 libsmbclient</h3>

<p>libsmbclient permits one to manipulate CIFS/SMB network resources.
</p>
<p>Following syntax is required.
</p>
<div class="example">
<pre class="example-preformatted">smb://[[domain:]user[:password@]]server[/share[/path[/file]]]
</pre></div>

<p>This protocol accepts the following options.
</p>
<dl class="table">
<dt><samp class="option">timeout</samp></dt>
<dd><p>Set timeout in milliseconds of socket I/O operations used by the underlying
low level operation. By default it is set to -1, which means that the timeout
is not specified.
</p>
</dd>
<dt><samp class="option">truncate</samp></dt>
<dd><p>Truncate existing files on write, if set to 1. A value of 0 prevents
truncating. Default value is 1.
</p>
</dd>
<dt><samp class="option">workgroup</samp></dt>
<dd><p>Set the workgroup used for making connections. By default workgroup is not specified.
</p>
</dd>
</dl>

<p>For more information see: <a class="url" href="http://www.samba.org/">http://www.samba.org/</a>.
</p>
</div>
<div class="section-level-extent" id="libssh">
<h3 class="section">3.31 libssh</h3>

<p>Secure File Transfer Protocol via libssh
</p>
<p>Read from or write to remote resources using SFTP protocol.
</p>
<p>Following syntax is required.
</p>
<div class="example">
<pre class="example-preformatted">sftp://[user[:password]@]server[:port]/path/to/remote/resource.mpeg
</pre></div>

<p>This protocol accepts the following options.
</p>
<dl class="table">
<dt><samp class="option">timeout</samp></dt>
<dd><p>Set timeout of socket I/O operations used by the underlying low level
operation. By default it is set to -1, which means that the timeout
is not specified.
</p>
</dd>
<dt><samp class="option">truncate</samp></dt>
<dd><p>Truncate existing files on write, if set to 1. A value of 0 prevents
truncating. Default value is 1.
</p>
</dd>
<dt><samp class="option">private_key</samp></dt>
<dd><p>Specify the path of the file containing private key to use during authorization.
By default libssh searches for keys in the <samp class="file">~/.ssh/</samp> directory.
</p>
</dd>
</dl>

<p>Example: Play a file stored on remote server.
</p>
<div class="example">
<pre class="example-preformatted">ffplay sftp://user:password@server_address:22/home/<USER>/resource.mpeg
</pre></div>

</div>
<div class="section-level-extent" id="librtmp-rtmp_002c-rtmpe_002c-rtmps_002c-rtmpt_002c-rtmpte">
<h3 class="section">3.32 librtmp rtmp, rtmpe, rtmps, rtmpt, rtmpte</h3>

<p>Real-Time Messaging Protocol and its variants supported through
librtmp.
</p>
<p>Requires the presence of the librtmp headers and library during
configuration. You need to explicitly configure the build with
&quot;&ndash;enable-librtmp&quot;. If enabled this will replace the native RTMP
protocol.
</p>
<p>This protocol provides most client functions and a few server
functions needed to support RTMP, RTMP tunneled in HTTP (RTMPT),
encrypted RTMP (RTMPE), RTMP over SSL/TLS (RTMPS) and tunneled
variants of these encrypted types (RTMPTE, RTMPTS).
</p>
<p>The required syntax is:
</p><div class="example">
<pre class="example-preformatted"><var class="var">rtmp_proto</var>://<var class="var">server</var>[:<var class="var">port</var>][/<var class="var">app</var>][/<var class="var">playpath</var>] <var class="var">options</var>
</pre></div>

<p>where <var class="var">rtmp_proto</var> is one of the strings &quot;rtmp&quot;, &quot;rtmpt&quot;, &quot;rtmpe&quot;,
&quot;rtmps&quot;, &quot;rtmpte&quot;, &quot;rtmpts&quot; corresponding to each RTMP variant, and
<var class="var">server</var>, <var class="var">port</var>, <var class="var">app</var> and <var class="var">playpath</var> have the same
meaning as specified for the RTMP native protocol.
<var class="var">options</var> contains a list of space-separated options of the form
<var class="var">key</var>=<var class="var">val</var>.
</p>
<p>See the librtmp manual page (man 3 librtmp) for more information.
</p>
<p>For example, to stream a file in real-time to an RTMP server using
<code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i myfile -f flv rtmp://myserver/live/mystream
</pre></div>

<p>To play the same stream using <code class="command">ffplay</code>:
</p><div class="example">
<pre class="example-preformatted">ffplay &quot;rtmp://myserver/live/mystream live=1&quot;
</pre></div>

</div>
<div class="section-level-extent" id="rtp">
<h3 class="section">3.33 rtp</h3>

<p>Real-time Transport Protocol.
</p>
<p>The required syntax for an RTP URL is:
rtp://<var class="var">hostname</var>[:<var class="var">port</var>][?<var class="var">option</var>=<var class="var">val</var>...]
</p>
<p><var class="var">port</var> specifies the RTP port to use.
</p>
<p>The following URL options are supported:
</p>
<dl class="table">
<dt><samp class="option">ttl=<var class="var">n</var></samp></dt>
<dd><p>Set the TTL (Time-To-Live) value (for multicast only).
</p>
</dd>
<dt><samp class="option">rtcpport=<var class="var">n</var></samp></dt>
<dd><p>Set the remote RTCP port to <var class="var">n</var>.
</p>
</dd>
<dt><samp class="option">localrtpport=<var class="var">n</var></samp></dt>
<dd><p>Set the local RTP port to <var class="var">n</var>.
</p>
</dd>
<dt><samp class="option">localrtcpport=<var class="var">n</var>'</samp></dt>
<dd><p>Set the local RTCP port to <var class="var">n</var>.
</p>
</dd>
<dt><samp class="option">pkt_size=<var class="var">n</var></samp></dt>
<dd><p>Set max packet size (in bytes) to <var class="var">n</var>.
</p>
</dd>
<dt><samp class="option">buffer_size=<var class="var">size</var></samp></dt>
<dd><p>Set the maximum UDP socket buffer size in bytes.
</p>
</dd>
<dt><samp class="option">connect=0|1</samp></dt>
<dd><p>Do a <code class="code">connect()</code> on the UDP socket (if set to 1) or not (if set
to 0).
</p>
</dd>
<dt><samp class="option">sources=<var class="var">ip</var>[,<var class="var">ip</var>]</samp></dt>
<dd><p>List allowed source IP addresses.
</p>
</dd>
<dt><samp class="option">block=<var class="var">ip</var>[,<var class="var">ip</var>]</samp></dt>
<dd><p>List disallowed (blocked) source IP addresses.
</p>
</dd>
<dt><samp class="option">write_to_source=0|1</samp></dt>
<dd><p>Send packets to the source address of the latest received packet (if
set to 1) or to a default remote address (if set to 0).
</p>
</dd>
<dt><samp class="option">localport=<var class="var">n</var></samp></dt>
<dd><p>Set the local RTP port to <var class="var">n</var>.
</p>
</dd>
<dt><samp class="option">localaddr=<var class="var">addr</var></samp></dt>
<dd><p>Local IP address of a network interface used for sending packets or joining
multicast groups.
</p>
</dd>
<dt><samp class="option">timeout=<var class="var">n</var></samp></dt>
<dd><p>Set timeout (in microseconds) of socket I/O operations to <var class="var">n</var>.
</p>
<p>This is a deprecated option. Instead, <samp class="option">localrtpport</samp> should be
used.
</p>
</dd>
</dl>

<p>Important notes:
</p>
<ol class="enumerate">
<li> If <samp class="option">rtcpport</samp> is not set the RTCP port will be set to the RTP
port value plus 1.

</li><li> If <samp class="option">localrtpport</samp> (the local RTP port) is not set any available
port will be used for the local RTP and RTCP ports.

</li><li> If <samp class="option">localrtcpport</samp> (the local RTCP port) is not set it will be
set to the local RTP port value plus 1.
</li></ol>

</div>
<div class="section-level-extent" id="rtsp">
<h3 class="section">3.34 rtsp</h3>

<p>Real-Time Streaming Protocol.
</p>
<p>RTSP is not technically a protocol handler in libavformat, it is a demuxer
and muxer. The demuxer supports both normal RTSP (with data transferred
over RTP; this is used by e.g. Apple and Microsoft) and Real-RTSP (with
data transferred over RDT).
</p>
<p>The muxer can be used to send a stream using RTSP ANNOUNCE to a server
supporting it (currently Darwin Streaming Server and Mischa Spiegelmock&rsquo;s
<a class="uref" href="https://github.com/revmischa/rtsp-server">RTSP server</a>).
</p>
<p>The required syntax for a RTSP url is:
</p><div class="example">
<pre class="example-preformatted">rtsp://<var class="var">hostname</var>[:<var class="var">port</var>]/<var class="var">path</var>
</pre></div>

<p>Options can be set on the <code class="command">ffmpeg</code>/<code class="command">ffplay</code> command
line, or set in code via <code class="code">AVOption</code>s or in
<code class="code">avformat_open_input</code>.
</p>
<ul class="mini-toc">
<li><a href="#Muxer" accesskey="1">Muxer</a></li>
<li><a href="#Demuxer" accesskey="2">Demuxer</a></li>
<li><a href="#Examples" accesskey="3">Examples</a></li>
</ul>
<div class="subsection-level-extent" id="Muxer">
<h4 class="subsection">3.34.1 Muxer</h4>
<p>The following options are supported.
</p>
<dl class="table">
<dt><samp class="option">rtsp_transport</samp></dt>
<dd><p>Set RTSP transport protocols.
</p>
<p>It accepts the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">udp</samp>&rsquo;</dt>
<dd><p>Use UDP as lower transport protocol.
</p>
</dd>
<dt>&lsquo;<samp class="samp">tcp</samp>&rsquo;</dt>
<dd><p>Use TCP (interleaving within the RTSP control channel) as lower
transport protocol.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">0</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">rtsp_flags</samp></dt>
<dd><p>Set RTSP flags.
</p>
<p>The following values are accepted:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">latm</samp>&rsquo;</dt>
<dd><p>Use MP4A-LATM packetization instead of MPEG4-GENERIC for AAC.
</p></dd>
<dt>&lsquo;<samp class="samp">rfc2190</samp>&rsquo;</dt>
<dd><p>Use RFC 2190 packetization instead of RFC 4629 for H.263.
</p></dd>
<dt>&lsquo;<samp class="samp">skip_rtcp</samp>&rsquo;</dt>
<dd><p>Don&rsquo;t send RTCP sender reports.
</p></dd>
<dt>&lsquo;<samp class="samp">h264_mode0</samp>&rsquo;</dt>
<dd><p>Use mode 0 for H.264 in RTP.
</p></dd>
<dt>&lsquo;<samp class="samp">send_bye</samp>&rsquo;</dt>
<dd><p>Send RTCP BYE packets when finishing.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">0</samp>&rsquo;.
</p>

</dd>
<dt><samp class="option">min_port</samp></dt>
<dd><p>Set minimum local UDP port. Default value is 5000.
</p>
</dd>
<dt><samp class="option">max_port</samp></dt>
<dd><p>Set maximum local UDP port. Default value is 65000.
</p>
</dd>
<dt><samp class="option">buffer_size</samp></dt>
<dd><p>Set the maximum socket buffer size in bytes.
</p>
</dd>
<dt><samp class="option">pkt_size</samp></dt>
<dd><p>Set max send packet size (in bytes). Default value is 1472.
</p></dd>
</dl>

</div>
<div class="subsection-level-extent" id="Demuxer">
<h4 class="subsection">3.34.2 Demuxer</h4>
<p>The following options are supported.
</p>
<dl class="table">
<dt><samp class="option">initial_pause</samp></dt>
<dd><p>Do not start playing the stream immediately if set to 1. Default value
is 0.
</p>
</dd>
<dt><samp class="option">rtsp_transport</samp></dt>
<dd><p>Set RTSP transport protocols.
</p>
<p>It accepts the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">udp</samp>&rsquo;</dt>
<dd><p>Use UDP as lower transport protocol.
</p>
</dd>
<dt>&lsquo;<samp class="samp">tcp</samp>&rsquo;</dt>
<dd><p>Use TCP (interleaving within the RTSP control channel) as lower
transport protocol.
</p>
</dd>
<dt>&lsquo;<samp class="samp">udp_multicast</samp>&rsquo;</dt>
<dd><p>Use UDP multicast as lower transport protocol.
</p>
</dd>
<dt>&lsquo;<samp class="samp">http</samp>&rsquo;</dt>
<dd><p>Use HTTP tunneling as lower transport protocol, which is useful for
passing proxies.
</p>
</dd>
<dt>&lsquo;<samp class="samp">https</samp>&rsquo;</dt>
<dd><p>Use HTTPs tunneling as lower transport protocol, which is useful for
passing proxies and widely used for security consideration.
</p></dd>
</dl>

<p>Multiple lower transport protocols may be specified, in that case they are
tried one at a time (if the setup of one fails, the next one is tried).
For the muxer, only the &lsquo;<samp class="samp">tcp</samp>&rsquo; and &lsquo;<samp class="samp">udp</samp>&rsquo; options are supported.
</p>
</dd>
<dt><samp class="option">rtsp_flags</samp></dt>
<dd><p>Set RTSP flags.
</p>
<p>The following values are accepted:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">filter_src</samp>&rsquo;</dt>
<dd><p>Accept packets only from negotiated peer address and port.
</p></dd>
<dt>&lsquo;<samp class="samp">listen</samp>&rsquo;</dt>
<dd><p>Act as a server, listening for an incoming connection.
</p></dd>
<dt>&lsquo;<samp class="samp">prefer_tcp</samp>&rsquo;</dt>
<dd><p>Try TCP for RTP transport first, if TCP is available as RTSP RTP transport.
</p></dd>
<dt>&lsquo;<samp class="samp">satip_raw</samp>&rsquo;</dt>
<dd><p>Export raw MPEG-TS stream instead of demuxing. The flag will simply write out
the raw stream, with the original PAT/PMT/PIDs intact.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">none</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">allowed_media_types</samp></dt>
<dd><p>Set media types to accept from the server.
</p>
<p>The following flags are accepted:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">video</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">audio</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">data</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">subtitle</samp>&rsquo;</dt>
</dl>

<p>By default it accepts all media types.
</p>
</dd>
<dt><samp class="option">min_port</samp></dt>
<dd><p>Set minimum local UDP port. Default value is 5000.
</p>
</dd>
<dt><samp class="option">max_port</samp></dt>
<dd><p>Set maximum local UDP port. Default value is 65000.
</p>
</dd>
<dt><samp class="option">listen_timeout</samp></dt>
<dd><p>Set maximum timeout (in seconds) to establish an initial connection. Setting
<samp class="option">listen_timeout</samp> &gt; 0 sets <samp class="option">rtsp_flags</samp> to &lsquo;<samp class="samp">listen</samp>&rsquo;. Default is -1
which means an infinite timeout when &lsquo;<samp class="samp">listen</samp>&rsquo; mode is set.
</p>
</dd>
<dt><samp class="option">reorder_queue_size</samp></dt>
<dd><p>Set number of packets to buffer for handling of reordered packets.
</p>
</dd>
<dt><samp class="option">timeout</samp></dt>
<dd><p>Set socket TCP I/O timeout in microseconds.
</p>
</dd>
<dt><samp class="option">user_agent</samp></dt>
<dd><p>Override User-Agent header. If not specified, it defaults to the
libavformat identifier string.
</p>
</dd>
<dt><samp class="option">buffer_size</samp></dt>
<dd><p>Set the maximum socket buffer size in bytes.
</p></dd>
</dl>

<p>When receiving data over UDP, the demuxer tries to reorder received packets
(since they may arrive out of order, or packets may get lost totally). This
can be disabled by setting the maximum demuxing delay to zero (via
the <code class="code">max_delay</code> field of AVFormatContext).
</p>
<p>When watching multi-bitrate Real-RTSP streams with <code class="command">ffplay</code>, the
streams to display can be chosen with <code class="code">-vst</code> <var class="var">n</var> and
<code class="code">-ast</code> <var class="var">n</var> for video and audio respectively, and can be switched
on the fly by pressing <code class="code">v</code> and <code class="code">a</code>.
</p>
</div>
<div class="subsection-level-extent" id="Examples">
<h4 class="subsection">3.34.3 Examples</h4>

<p>The following examples all make use of the <code class="command">ffplay</code> and
<code class="command">ffmpeg</code> tools.
</p>
<ul class="itemize mark-bullet">
<li>Watch a stream over UDP, with a max reordering delay of 0.5 seconds:
<div class="example">
<pre class="example-preformatted">ffplay -max_delay 500000 -rtsp_transport udp rtsp://server/video.mp4
</pre></div>

</li><li>Watch a stream tunneled over HTTP:
<div class="example">
<pre class="example-preformatted">ffplay -rtsp_transport http rtsp://server/video.mp4
</pre></div>

</li><li>Send a stream in realtime to a RTSP server, for others to watch:
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i <var class="var">input</var> -f rtsp -muxdelay 0.1 rtsp://server/live.sdp
</pre></div>

</li><li>Receive a stream in realtime:
<div class="example">
<pre class="example-preformatted">ffmpeg -rtsp_flags listen -i rtsp://ownaddress/live.sdp <var class="var">output</var>
</pre></div>
</li></ul>

</div>
</div>
<div class="section-level-extent" id="sap">
<h3 class="section">3.35 sap</h3>

<p>Session Announcement Protocol (RFC 2974). This is not technically a
protocol handler in libavformat, it is a muxer and demuxer.
It is used for signalling of RTP streams, by announcing the SDP for the
streams regularly on a separate port.
</p>
<ul class="mini-toc">
<li><a href="#Muxer-1" accesskey="1">Muxer</a></li>
<li><a href="#Demuxer-1" accesskey="2">Demuxer</a></li>
</ul>
<div class="subsection-level-extent" id="Muxer-1">
<h4 class="subsection">3.35.1 Muxer</h4>

<p>The syntax for a SAP url given to the muxer is:
</p><div class="example">
<pre class="example-preformatted">sap://<var class="var">destination</var>[:<var class="var">port</var>][?<var class="var">options</var>]
</pre></div>

<p>The RTP packets are sent to <var class="var">destination</var> on port <var class="var">port</var>,
or to port 5004 if no port is specified.
<var class="var">options</var> is a <code class="code">&amp;</code>-separated list. The following options
are supported:
</p>
<dl class="table">
<dt><samp class="option">announce_addr=<var class="var">address</var></samp></dt>
<dd><p>Specify the destination IP address for sending the announcements to.
If omitted, the announcements are sent to the commonly used SAP
announcement multicast address ************* (sap.mcast.net), or
ff0e::2:7ffe if <var class="var">destination</var> is an IPv6 address.
</p>
</dd>
<dt><samp class="option">announce_port=<var class="var">port</var></samp></dt>
<dd><p>Specify the port to send the announcements on, defaults to
9875 if not specified.
</p>
</dd>
<dt><samp class="option">ttl=<var class="var">ttl</var></samp></dt>
<dd><p>Specify the time to live value for the announcements and RTP packets,
defaults to 255.
</p>
</dd>
<dt><samp class="option">same_port=<var class="var">0|1</var></samp></dt>
<dd><p>If set to 1, send all RTP streams on the same port pair. If zero (the
default), all streams are sent on unique ports, with each stream on a
port 2 numbers higher than the previous.
VLC/Live555 requires this to be set to 1, to be able to receive the stream.
The RTP stack in libavformat for receiving requires all streams to be sent
on unique ports.
</p></dd>
</dl>

<p>Example command lines follow.
</p>
<p>To broadcast a stream on the local subnet, for watching in VLC:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i <var class="var">input</var> -f sap sap://***********?same_port=1
</pre></div>

<p>Similarly, for watching in <code class="command">ffplay</code>:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i <var class="var">input</var> -f sap sap://***********
</pre></div>

<p>And for watching in <code class="command">ffplay</code>, over IPv6:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i <var class="var">input</var> -f sap sap://[ff0e::1:2:3:4]
</pre></div>

</div>
<div class="subsection-level-extent" id="Demuxer-1">
<h4 class="subsection">3.35.2 Demuxer</h4>

<p>The syntax for a SAP url given to the demuxer is:
</p><div class="example">
<pre class="example-preformatted">sap://[<var class="var">address</var>][:<var class="var">port</var>]
</pre></div>

<p><var class="var">address</var> is the multicast address to listen for announcements on,
if omitted, the default ************* (sap.mcast.net) is used. <var class="var">port</var>
is the port that is listened on, 9875 if omitted.
</p>
<p>The demuxers listens for announcements on the given address and port.
Once an announcement is received, it tries to receive that particular stream.
</p>
<p>Example command lines follow.
</p>
<p>To play back the first stream announced on the normal SAP multicast address:
</p>
<div class="example">
<pre class="example-preformatted">ffplay sap://
</pre></div>

<p>To play back the first stream announced on one the default IPv6 SAP multicast address:
</p>
<div class="example">
<pre class="example-preformatted">ffplay sap://[ff0e::2:7ffe]
</pre></div>

</div>
</div>
<div class="section-level-extent" id="sctp">
<h3 class="section">3.36 sctp</h3>

<p>Stream Control Transmission Protocol.
</p>
<p>The accepted URL syntax is:
</p><div class="example">
<pre class="example-preformatted">sctp://<var class="var">host</var>:<var class="var">port</var>[?<var class="var">options</var>]
</pre></div>

<p>The protocol accepts the following options:
</p><dl class="table">
<dt><samp class="option">listen</samp></dt>
<dd><p>If set to any value, listen for an incoming connection. Outgoing connection is done by default.
</p>
</dd>
<dt><samp class="option">max_streams</samp></dt>
<dd><p>Set the maximum number of streams. By default no limit is set.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="srt">
<h3 class="section">3.37 srt</h3>

<p>Haivision Secure Reliable Transport Protocol via libsrt.
</p>
<p>The supported syntax for a SRT URL is:
</p><div class="example">
<pre class="example-preformatted">srt://<var class="var">hostname</var>:<var class="var">port</var>[?<var class="var">options</var>]
</pre></div>

<p><var class="var">options</var> contains a list of &amp;-separated options of the form
<var class="var">key</var>=<var class="var">val</var>.
</p>
<p>or
</p>
<div class="example">
<pre class="example-preformatted"><var class="var">options</var> srt://<var class="var">hostname</var>:<var class="var">port</var>
</pre></div>

<p><var class="var">options</var> contains a list of &rsquo;-<var class="var">key</var> <var class="var">val</var>&rsquo;
options.
</p>
<p>This protocol accepts the following options.
</p>
<dl class="table">
<dt><samp class="option">connect_timeout=<var class="var">milliseconds</var></samp></dt>
<dd><p>Connection timeout; SRT cannot connect for RTT &gt; 1500 msec
(2 handshake exchanges) with the default connect timeout of
3 seconds. This option applies to the caller and rendezvous
connection modes. The connect timeout is 10 times the value
set for the rendezvous mode (which can be used as a
workaround for this connection problem with earlier versions).
</p>
</dd>
<dt><samp class="option">ffs=<var class="var">bytes</var></samp></dt>
<dd><p>Flight Flag Size (Window Size), in bytes. FFS is actually an
internal parameter and you should set it to not less than
<samp class="option">recv_buffer_size</samp> and <samp class="option">mss</samp>. The default value
is relatively large, therefore unless you set a very large receiver buffer,
you do not need to change this option. Default value is 25600.
</p>
</dd>
<dt><samp class="option">inputbw=<var class="var">bytes/seconds</var></samp></dt>
<dd><p>Sender nominal input rate, in bytes per seconds. Used along with
<samp class="option">oheadbw</samp>, when <samp class="option">maxbw</samp> is set to relative (0), to
calculate maximum sending rate when recovery packets are sent
along with the main media stream:
<samp class="option">inputbw</samp> * (100 + <samp class="option">oheadbw</samp>) / 100
if <samp class="option">inputbw</samp> is not set while <samp class="option">maxbw</samp> is set to
relative (0), the actual input rate is evaluated inside
the library. Default value is 0.
</p>
</dd>
<dt><samp class="option">iptos=<var class="var">tos</var></samp></dt>
<dd><p>IP Type of Service. Applies to sender only. Default value is 0xB8.
</p>
</dd>
<dt><samp class="option">ipttl=<var class="var">ttl</var></samp></dt>
<dd><p>IP Time To Live. Applies to sender only. Default value is 64.
</p>
</dd>
<dt><samp class="option">latency=<var class="var">microseconds</var></samp></dt>
<dd><p>Timestamp-based Packet Delivery Delay.
Used to absorb bursts of missed packet retransmissions.
This flag sets both <samp class="option">rcvlatency</samp> and <samp class="option">peerlatency</samp>
to the same value. Note that prior to version 1.3.0
this is the only flag to set the latency, however
this is effectively equivalent to setting <samp class="option">peerlatency</samp>,
when side is sender and <samp class="option">rcvlatency</samp>
when side is receiver, and the bidirectional stream
sending is not supported.
</p>
</dd>
<dt><samp class="option">listen_timeout=<var class="var">microseconds</var></samp></dt>
<dd><p>Set socket listen timeout.
</p>
</dd>
<dt><samp class="option">maxbw=<var class="var">bytes/seconds</var></samp></dt>
<dd><p>Maximum sending bandwidth, in bytes per seconds.
-1 infinite (CSRTCC limit is 30mbps)
0 relative to input rate (see <samp class="option">inputbw</samp>)
&gt;0 absolute limit value
Default value is 0 (relative)
</p>
</dd>
<dt><samp class="option">mode=<var class="var">caller|listener|rendezvous</var></samp></dt>
<dd><p>Connection mode.
<samp class="option">caller</samp> opens client connection.
<samp class="option">listener</samp> starts server to listen for incoming connections.
<samp class="option">rendezvous</samp> use Rendez-Vous connection mode.
Default value is caller.
</p>
</dd>
<dt><samp class="option">mss=<var class="var">bytes</var></samp></dt>
<dd><p>Maximum Segment Size, in bytes. Used for buffer allocation
and rate calculation using a packet counter assuming fully
filled packets. The smallest MSS between the peers is
used. This is 1500 by default in the overall internet.
This is the maximum size of the UDP packet and can be
only decreased, unless you have some unusual dedicated
network settings. Default value is 1500.
</p>
</dd>
<dt><samp class="option">nakreport=<var class="var">1|0</var></samp></dt>
<dd><p>If set to 1, Receiver will send &lsquo;UMSG_LOSSREPORT&lsquo; messages
periodically until a lost packet is retransmitted or
intentionally dropped. Default value is 1.
</p>
</dd>
<dt><samp class="option">oheadbw=<var class="var">percents</var></samp></dt>
<dd><p>Recovery bandwidth overhead above input rate, in percents.
See <samp class="option">inputbw</samp>. Default value is 25%.
</p>
</dd>
<dt><samp class="option">passphrase=<var class="var">string</var></samp></dt>
<dd><p>HaiCrypt Encryption/Decryption Passphrase string, length
from 10 to 79 characters. The passphrase is the shared
secret between the sender and the receiver. It is used
to generate the Key Encrypting Key using PBKDF2
(Password-Based Key Derivation Function). It is used
only if <samp class="option">pbkeylen</samp> is non-zero. It is used on
the receiver only if the received data is encrypted.
The configured passphrase cannot be recovered (write-only).
</p>
</dd>
<dt><samp class="option">enforced_encryption=<var class="var">1|0</var></samp></dt>
<dd><p>If true, both connection parties must have the same password
set (including empty, that is, with no encryption). If the
password doesn&rsquo;t match or only one side is unencrypted,
the connection is rejected. Default is true.
</p>
</dd>
<dt><samp class="option">kmrefreshrate=<var class="var">packets</var></samp></dt>
<dd><p>The number of packets to be transmitted after which the
encryption key is switched to a new key. Default is -1.
-1 means auto (0x1000000 in srt library). The range for
this option is integers in the 0 - <code class="code">INT_MAX</code>.
</p>
</dd>
<dt><samp class="option">kmpreannounce=<var class="var">packets</var></samp></dt>
<dd><p>The interval between when a new encryption key is sent and
when switchover occurs. This value also applies to the
subsequent interval between when switchover occurs and
when the old encryption key is decommissioned. Default is -1.
-1 means auto (0x1000 in srt library). The range for
this option is integers in the 0 - <code class="code">INT_MAX</code>.
</p>
</dd>
<dt><samp class="option">snddropdelay=<var class="var">microseconds</var></samp></dt>
<dd><p>The sender&rsquo;s extra delay before dropping packets. This delay is
added to the default drop delay time interval value.
</p>
<p>Special value -1: Do not drop packets on the sender at all.
</p>
</dd>
<dt><samp class="option">payload_size=<var class="var">bytes</var></samp></dt>
<dd><p>Sets the maximum declared size of a packet transferred
during the single call to the sending function in Live
mode. Use 0 if this value isn&rsquo;t used (which is default in
file mode).
Default is -1 (automatic), which typically means MPEG-TS;
if you are going to use SRT
to send any different kind of payload, such as, for example,
wrapping a live stream in very small frames, then you can
use a bigger maximum frame size, though not greater than
1456 bytes.
</p>
</dd>
<dt><samp class="option">pkt_size=<var class="var">bytes</var></samp></dt>
<dd><p>Alias for &lsquo;<samp class="samp">payload_size</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">peerlatency=<var class="var">microseconds</var></samp></dt>
<dd><p>The latency value (as described in <samp class="option">rcvlatency</samp>) that is
set by the sender side as a minimum value for the receiver.
</p>
</dd>
<dt><samp class="option">pbkeylen=<var class="var">bytes</var></samp></dt>
<dd><p>Sender encryption key length, in bytes.
Only can be set to 0, 16, 24 and 32.
Enable sender encryption if not 0.
Not required on receiver (set to 0),
key size obtained from sender in HaiCrypt handshake.
Default value is 0.
</p>
</dd>
<dt><samp class="option">rcvlatency=<var class="var">microseconds</var></samp></dt>
<dd><p>The time that should elapse since the moment when the
packet was sent and the moment when it&rsquo;s delivered to
the receiver application in the receiving function.
This time should be a buffer time large enough to cover
the time spent for sending, unexpectedly extended RTT
time, and the time needed to retransmit the lost UDP
packet. The effective latency value will be the maximum
of this options&rsquo; value and the value of <samp class="option">peerlatency</samp>
set by the peer side. Before version 1.3.0 this option
is only available as <samp class="option">latency</samp>.
</p>
</dd>
<dt><samp class="option">recv_buffer_size=<var class="var">bytes</var></samp></dt>
<dd><p>Set UDP receive buffer size, expressed in bytes.
</p>
</dd>
<dt><samp class="option">send_buffer_size=<var class="var">bytes</var></samp></dt>
<dd><p>Set UDP send buffer size, expressed in bytes.
</p>
</dd>
<dt><samp class="option">timeout=<var class="var">microseconds</var></samp></dt>
<dd><p>Set raise error timeouts for read, write and connect operations. Note that the
SRT library has internal timeouts which can be controlled separately, the
value set here is only a cap on those.
</p>
</dd>
<dt><samp class="option">tlpktdrop=<var class="var">1|0</var></samp></dt>
<dd><p>Too-late Packet Drop. When enabled on receiver, it skips
missing packets that have not been delivered in time and
delivers the following packets to the application when
their time-to-play has come. It also sends a fake ACK to
the sender. When enabled on sender and enabled on the
receiving peer, the sender drops the older packets that
have no chance of being delivered in time. It was
automatically enabled in the sender if the receiver
supports it.
</p>
</dd>
<dt><samp class="option">sndbuf=<var class="var">bytes</var></samp></dt>
<dd><p>Set send buffer size, expressed in bytes.
</p>
</dd>
<dt><samp class="option">rcvbuf=<var class="var">bytes</var></samp></dt>
<dd><p>Set receive buffer size, expressed in bytes.
</p>
<p>Receive buffer must not be greater than <samp class="option">ffs</samp>.
</p>
</dd>
<dt><samp class="option">lossmaxttl=<var class="var">packets</var></samp></dt>
<dd><p>The value up to which the Reorder Tolerance may grow. When
Reorder Tolerance is &gt; 0, then packet loss report is delayed
until that number of packets come in. Reorder Tolerance
increases every time a &quot;belated&quot; packet has come, but it
wasn&rsquo;t due to retransmission (that is, when UDP packets tend
to come out of order), with the difference between the latest
sequence and this packet&rsquo;s sequence, and not more than the
value of this option. By default it&rsquo;s 0, which means that this
mechanism is turned off, and the loss report is always sent
immediately upon experiencing a &quot;gap&quot; in sequences.
</p>
</dd>
<dt><samp class="option">minversion</samp></dt>
<dd><p>The minimum SRT version that is required from the peer. A connection
to a peer that does not satisfy the minimum version requirement
will be rejected.
</p>
<p>The version format in hex is 0xXXYYZZ for x.y.z in human readable
form.
</p>
</dd>
<dt><samp class="option">streamid=<var class="var">string</var></samp></dt>
<dd><p>A string limited to 512 characters that can be set on the socket prior
to connecting. This stream ID will be able to be retrieved by the
listener side from the socket that is returned from srt_accept and
was connected by a socket with that set stream ID. SRT does not enforce
any special interpretation of the contents of this string.
This option doesn’t make sense in Rendezvous connection; the result
might be that simply one side will override the value from the other
side and it’s the matter of luck which one would win
</p>
</dd>
<dt><samp class="option">srt_streamid=<var class="var">string</var></samp></dt>
<dd><p>Alias for &lsquo;<samp class="samp">streamid</samp>&rsquo; to avoid conflict with ffmpeg command line option.
</p>
</dd>
<dt><samp class="option">smoother=<var class="var">live|file</var></samp></dt>
<dd><p>The type of Smoother used for the transmission for that socket, which
is responsible for the transmission and congestion control. The Smoother
type must be exactly the same on both connecting parties, otherwise
the connection is rejected.
</p>
</dd>
<dt><samp class="option">messageapi=<var class="var">1|0</var></samp></dt>
<dd><p>When set, this socket uses the Message API, otherwise it uses Buffer
API. Note that in live mode (see <samp class="option">transtype</samp>) there’s only
message API available. In File mode you can chose to use one of two modes:
</p>
<p>Stream API (default, when this option is false). In this mode you may
send as many data as you wish with one sending instruction, or even use
dedicated functions that read directly from a file. The internal facility
will take care of any speed and congestion control. When receiving, you
can also receive as many data as desired, the data not extracted will be
waiting for the next call. There is no boundary between data portions in
the Stream mode.
</p>
<p>Message API. In this mode your single sending instruction passes exactly
one piece of data that has boundaries (a message). Contrary to Live mode,
this message may span across multiple UDP packets and the only size
limitation is that it shall fit as a whole in the sending buffer. The
receiver shall use as large buffer as necessary to receive the message,
otherwise the message will not be given up. When the message is not
complete (not all packets received or there was a packet loss) it will
not be given up.
</p>
</dd>
<dt><samp class="option">transtype=<var class="var">live|file</var></samp></dt>
<dd><p>Sets the transmission type for the socket, in particular, setting this
option sets multiple other parameters to their default values as required
for a particular transmission type.
</p>
<p>live: Set options as for live transmission. In this mode, you should
send by one sending instruction only so many data that fit in one UDP packet,
and limited to the value defined first in <samp class="option">payload_size</samp> (1316 is
default in this mode). There is no speed control in this mode, only the
bandwidth control, if configured, in order to not exceed the bandwidth with
the overhead transmission (retransmitted and control packets).
</p>
<p>file: Set options as for non-live transmission. See <samp class="option">messageapi</samp>
for further explanations
</p>
</dd>
<dt><samp class="option">linger=<var class="var">seconds</var></samp></dt>
<dd><p>The number of seconds that the socket waits for unsent data when closing.
Default is -1. -1 means auto (off with 0 seconds in live mode, on with 180
seconds in file mode). The range for this option is integers in the
0 - <code class="code">INT_MAX</code>.
</p>
</dd>
<dt><samp class="option">tsbpd=<var class="var">1|0</var></samp></dt>
<dd><p>When true, use Timestamp-based Packet Delivery mode. The default behavior
depends on the transmission type: enabled in live mode, disabled in file
mode.
</p>
</dd>
</dl>

<p>For more information see: <a class="url" href="https://github.com/Haivision/srt">https://github.com/Haivision/srt</a>.
</p>
</div>
<div class="section-level-extent" id="srtp">
<h3 class="section">3.38 srtp</h3>

<p>Secure Real-time Transport Protocol.
</p>
<p>The accepted options are:
</p><dl class="table">
<dt><samp class="option">srtp_in_suite</samp></dt>
<dt><samp class="option">srtp_out_suite</samp></dt>
<dd><p>Select input and output encoding suites.
</p>
<p>Supported values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">AES_CM_128_HMAC_SHA1_80</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">SRTP_AES128_CM_HMAC_SHA1_80</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">AES_CM_128_HMAC_SHA1_32</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">SRTP_AES128_CM_HMAC_SHA1_32</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">srtp_in_params</samp></dt>
<dt><samp class="option">srtp_out_params</samp></dt>
<dd><p>Set input and output encoding parameters, which are expressed by a
base64-encoded representation of a binary block. The first 16 bytes of
this binary block are used as master key, the following 14 bytes are
used as master salt.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="subfile">
<h3 class="section">3.39 subfile</h3>

<p>Virtually extract a segment of a file or another stream.
The underlying stream must be seekable.
</p>
<p>Accepted options:
</p><dl class="table">
<dt><samp class="option">start</samp></dt>
<dd><p>Start offset of the extracted segment, in bytes.
</p></dd>
<dt><samp class="option">end</samp></dt>
<dd><p>End offset of the extracted segment, in bytes.
If set to 0, extract till end of file.
</p></dd>
</dl>

<p>Examples:
</p>
<p>Extract a chapter from a DVD VOB file (start and end sectors obtained
externally and multiplied by 2048):
</p><div class="example">
<pre class="example-preformatted">subfile,,start,153391104,end,268142592,,:/media/dvd/VIDEO_TS/VTS_08_1.VOB
</pre></div>

<p>Play an AVI file directly from a TAR archive:
</p><div class="example">
<pre class="example-preformatted">subfile,,start,183241728,end,366490624,,:archive.tar
</pre></div>

<p>Play a MPEG-TS file from start offset till end:
</p><div class="example">
<pre class="example-preformatted">subfile,,start,32815239,end,0,,:video.ts
</pre></div>

</div>
<div class="section-level-extent" id="tee">
<h3 class="section">3.40 tee</h3>

<p>Writes the output to multiple protocols. The individual outputs are separated
by |
</p>
<div class="example">
<pre class="example-preformatted">tee:file://path/to/local/this.avi|file://path/to/local/that.avi
</pre></div>

</div>
<div class="section-level-extent" id="tcp">
<h3 class="section">3.41 tcp</h3>

<p>Transmission Control Protocol.
</p>
<p>The required syntax for a TCP url is:
</p><div class="example">
<pre class="example-preformatted">tcp://<var class="var">hostname</var>:<var class="var">port</var>[?<var class="var">options</var>]
</pre></div>

<p><var class="var">options</var> contains a list of &amp;-separated options of the form
<var class="var">key</var>=<var class="var">val</var>.
</p>
<p>The list of supported options follows.
</p>
<dl class="table">
<dt><samp class="option">listen=<var class="var">2|1|0</var></samp></dt>
<dd><p>Listen for an incoming connection. 0 disables listen, 1 enables listen in
single client mode, 2 enables listen in multi-client mode. Default value is 0.
</p>
</dd>
<dt><samp class="option">timeout=<var class="var">microseconds</var></samp></dt>
<dd><p>Set raise error timeout, expressed in microseconds.
</p>
<p>This option is only relevant in read mode: if no data arrived in more
than this time interval, raise error.
</p>
</dd>
<dt><samp class="option">listen_timeout=<var class="var">milliseconds</var></samp></dt>
<dd><p>Set listen timeout, expressed in milliseconds.
</p>
</dd>
<dt><samp class="option">recv_buffer_size=<var class="var">bytes</var></samp></dt>
<dd><p>Set receive buffer size, expressed bytes.
</p>
</dd>
<dt><samp class="option">send_buffer_size=<var class="var">bytes</var></samp></dt>
<dd><p>Set send buffer size, expressed bytes.
</p>
</dd>
<dt><samp class="option">tcp_nodelay=<var class="var">1|0</var></samp></dt>
<dd><p>Set TCP_NODELAY to disable Nagle&rsquo;s algorithm. Default value is 0.
</p>
<p><em class="emph">Remark: Writing to the socket is currently not optimized to minimize system calls and reduces the efficiency / effect of TCP_NODELAY.</em>
</p>
</dd>
<dt><samp class="option">tcp_mss=<var class="var">bytes</var></samp></dt>
<dd><p>Set maximum segment size for outgoing TCP packets, expressed in bytes.
</p></dd>
</dl>

<p>The following example shows how to setup a listening TCP connection
with <code class="command">ffmpeg</code>, which is then accessed with <code class="command">ffplay</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i <var class="var">input</var> -f <var class="var">format</var> tcp://<var class="var">hostname</var>:<var class="var">port</var>?listen
ffplay tcp://<var class="var">hostname</var>:<var class="var">port</var>
</pre></div>

</div>
<div class="section-level-extent" id="tls">
<h3 class="section">3.42 tls</h3>

<p>Transport Layer Security (TLS) / Secure Sockets Layer (SSL)
</p>
<p>The required syntax for a TLS/SSL url is:
</p><div class="example">
<pre class="example-preformatted">tls://<var class="var">hostname</var>:<var class="var">port</var>[?<var class="var">options</var>]
</pre></div>

<p>The following parameters can be set via command line options
(or in code via <code class="code">AVOption</code>s):
</p>
<dl class="table">
<dt><samp class="option">ca_file, cafile=<var class="var">filename</var></samp></dt>
<dd><p>A file containing certificate authority (CA) root certificates to treat
as trusted. If the linked TLS library contains a default this might not
need to be specified for verification to work, but not all libraries and
setups have defaults built in.
The file must be in OpenSSL PEM format.
</p>
</dd>
<dt><samp class="option">tls_verify=<var class="var">1|0</var></samp></dt>
<dd><p>If enabled, try to verify the peer that we are communicating with.
Note, if using OpenSSL, this currently only makes sure that the
peer certificate is signed by one of the root certificates in the CA
database, but it does not validate that the certificate actually
matches the host name we are trying to connect to. (With other backends,
the host name is validated as well.)
</p>
<p>This is disabled by default since it requires a CA database to be
provided by the caller in many cases.
</p>
</dd>
<dt><samp class="option">cert_file, cert=<var class="var">filename</var></samp></dt>
<dd><p>A file containing a certificate to use in the handshake with the peer.
(When operating as server, in listen mode, this is more often required
by the peer, while client certificates only are mandated in certain
setups.)
</p>
</dd>
<dt><samp class="option">key_file, key=<var class="var">filename</var></samp></dt>
<dd><p>A file containing the private key for the certificate.
</p>
</dd>
<dt><samp class="option">listen=<var class="var">1|0</var></samp></dt>
<dd><p>If enabled, listen for connections on the provided port, and assume
the server role in the handshake instead of the client role.
</p>
</dd>
<dt><samp class="option">http_proxy</samp></dt>
<dd><p>The HTTP proxy to tunnel through, e.g. <code class="code">http://example.com:1234</code>.
The proxy must support the CONNECT method.
</p>
</dd>
</dl>

<p>Example command lines:
</p>
<p>To create a TLS/SSL server that serves an input stream.
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i <var class="var">input</var> -f <var class="var">format</var> tls://<var class="var">hostname</var>:<var class="var">port</var>?listen&amp;cert=<var class="var">server.crt</var>&amp;key=<var class="var">server.key</var>
</pre></div>

<p>To play back a stream from the TLS/SSL server using <code class="command">ffplay</code>:
</p>
<div class="example">
<pre class="example-preformatted">ffplay tls://<var class="var">hostname</var>:<var class="var">port</var>
</pre></div>

</div>
<div class="section-level-extent" id="udp">
<h3 class="section">3.43 udp</h3>

<p>User Datagram Protocol.
</p>
<p>The required syntax for an UDP URL is:
</p><div class="example">
<pre class="example-preformatted">udp://<var class="var">hostname</var>:<var class="var">port</var>[?<var class="var">options</var>]
</pre></div>

<p><var class="var">options</var> contains a list of &amp;-separated options of the form <var class="var">key</var>=<var class="var">val</var>.
</p>
<p>In case threading is enabled on the system, a circular buffer is used
to store the incoming data, which allows one to reduce loss of data due to
UDP socket buffer overruns. The <var class="var">fifo_size</var> and
<var class="var">overrun_nonfatal</var> options are related to this buffer.
</p>
<p>The list of supported options follows.
</p>
<dl class="table">
<dt><samp class="option">buffer_size=<var class="var">size</var></samp></dt>
<dd><p>Set the UDP maximum socket buffer size in bytes. This is used to set either
the receive or send buffer size, depending on what the socket is used for.
Default is 32 KB for output, 384 KB for input.  See also <var class="var">fifo_size</var>.
</p>
</dd>
<dt><samp class="option">bitrate=<var class="var">bitrate</var></samp></dt>
<dd><p>If set to nonzero, the output will have the specified constant bitrate if the
input has enough packets to sustain it.
</p>
</dd>
<dt><samp class="option">burst_bits=<var class="var">bits</var></samp></dt>
<dd><p>When using <var class="var">bitrate</var> this specifies the maximum number of bits in
packet bursts.
</p>
</dd>
<dt><samp class="option">localport=<var class="var">port</var></samp></dt>
<dd><p>Override the local UDP port to bind with.
</p>
</dd>
<dt><samp class="option">localaddr=<var class="var">addr</var></samp></dt>
<dd><p>Local IP address of a network interface used for sending packets or joining
multicast groups.
</p>
</dd>
<dt><samp class="option">pkt_size=<var class="var">size</var></samp></dt>
<dd><p>Set the size in bytes of UDP packets.
</p>
</dd>
<dt><samp class="option">reuse=<var class="var">1|0</var></samp></dt>
<dd><p>Explicitly allow or disallow reusing UDP sockets.
</p>
</dd>
<dt><samp class="option">ttl=<var class="var">ttl</var></samp></dt>
<dd><p>Set the time to live value (for multicast only).
</p>
</dd>
<dt><samp class="option">connect=<var class="var">1|0</var></samp></dt>
<dd><p>Initialize the UDP socket with <code class="code">connect()</code>. In this case, the
destination address can&rsquo;t be changed with ff_udp_set_remote_url later.
If the destination address isn&rsquo;t known at the start, this option can
be specified in ff_udp_set_remote_url, too.
This allows finding out the source address for the packets with getsockname,
and makes writes return with AVERROR(ECONNREFUSED) if &quot;destination
unreachable&quot; is received.
For receiving, this gives the benefit of only receiving packets from
the specified peer address/port.
</p>
</dd>
<dt><samp class="option">sources=<var class="var">address</var>[,<var class="var">address</var>]</samp></dt>
<dd><p>Only receive packets sent from the specified addresses. In case of multicast,
also subscribe to multicast traffic coming from these addresses only.
</p>
</dd>
<dt><samp class="option">block=<var class="var">address</var>[,<var class="var">address</var>]</samp></dt>
<dd><p>Ignore packets sent from the specified addresses. In case of multicast, also
exclude the source addresses in the multicast subscription.
</p>
</dd>
<dt><samp class="option">fifo_size=<var class="var">units</var></samp></dt>
<dd><p>Set the UDP receiving circular buffer size, expressed as a number of
packets with size of 188 bytes. If not specified defaults to 7*4096.
</p>
</dd>
<dt><samp class="option">overrun_nonfatal=<var class="var">1|0</var></samp></dt>
<dd><p>Survive in case of UDP receiving circular buffer overrun. Default
value is 0.
</p>
</dd>
<dt><samp class="option">timeout=<var class="var">microseconds</var></samp></dt>
<dd><p>Set raise error timeout, expressed in microseconds.
</p>
<p>This option is only relevant in read mode: if no data arrived in more
than this time interval, raise error.
</p>
</dd>
<dt><samp class="option">broadcast=<var class="var">1|0</var></samp></dt>
<dd><p>Explicitly allow or disallow UDP broadcasting.
</p>
<p>Note that broadcasting may not work properly on networks having
a broadcast storm protection.
</p></dd>
</dl>

<ul class="mini-toc">
<li><a href="#Examples-1" accesskey="1">Examples</a></li>
</ul>
<div class="subsection-level-extent" id="Examples-1">
<h4 class="subsection">3.43.1 Examples</h4>

<ul class="itemize mark-bullet">
<li>Use <code class="command">ffmpeg</code> to stream over UDP to a remote endpoint:
<div class="example">
<pre class="example-preformatted">ffmpeg -i <var class="var">input</var> -f <var class="var">format</var> udp://<var class="var">hostname</var>:<var class="var">port</var>
</pre></div>

</li><li>Use <code class="command">ffmpeg</code> to stream in mpegts format over UDP using 188
sized UDP packets, using a large input buffer:
<div class="example">
<pre class="example-preformatted">ffmpeg -i <var class="var">input</var> -f mpegts udp://<var class="var">hostname</var>:<var class="var">port</var>?pkt_size=188&amp;buffer_size=65535
</pre></div>

</li><li>Use <code class="command">ffmpeg</code> to receive over UDP from a remote endpoint:
<div class="example">
<pre class="example-preformatted">ffmpeg -i udp://[<var class="var">multicast-address</var>]:<var class="var">port</var> ...
</pre></div>
</li></ul>

</div>
</div>
<div class="section-level-extent" id="unix">
<h3 class="section">3.44 unix</h3>

<p>Unix local socket
</p>
<p>The required syntax for a Unix socket URL is:
</p>
<div class="example">
<pre class="example-preformatted">unix://<var class="var">filepath</var>
</pre></div>

<p>The following parameters can be set via command line options
(or in code via <code class="code">AVOption</code>s):
</p>
<dl class="table">
<dt><samp class="option">timeout</samp></dt>
<dd><p>Timeout in ms.
</p></dd>
<dt><samp class="option">listen</samp></dt>
<dd><p>Create the Unix socket in listening mode.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="zmq">
<h3 class="section">3.45 zmq</h3>

<p>ZeroMQ asynchronous messaging using the libzmq library.
</p>
<p>This library supports unicast streaming to multiple clients without relying on
an external server.
</p>
<p>The required syntax for streaming or connecting to a stream is:
</p><div class="example">
<pre class="example-preformatted">zmq:tcp://ip-address:port
</pre></div>

<p>Example:
Create a localhost stream on port 5555:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i input -f mpegts zmq:tcp://127.0.0.1:5555
</pre></div>

<p>Multiple clients may connect to the stream using:
</p><div class="example">
<pre class="example-preformatted">ffplay zmq:tcp://127.0.0.1:5555
</pre></div>

<p>Streaming to multiple clients is implemented using a ZeroMQ Pub-Sub pattern.
The server side binds to a port and publishes data. Clients connect to the
server (via IP address/port) and subscribe to the stream. The order in which
the server and client start generally does not matter.
</p>
<p>ffmpeg must be compiled with the &ndash;enable-libzmq option to support
this protocol.
</p>
<p>Options can be set on the <code class="command">ffmpeg</code>/<code class="command">ffplay</code> command
line. The following options are supported:
</p>
<dl class="table">
<dt><samp class="option">pkt_size</samp></dt>
<dd><p>Forces the maximum packet size for sending/receiving data. The default value is
131,072 bytes. On the server side, this sets the maximum size of sent packets
via ZeroMQ. On the clients, it sets an internal buffer size for receiving
packets. Note that pkt_size on the clients should be equal to or greater than
pkt_size on the server. Otherwise the received message may be truncated causing
decoding errors.
</p>
</dd>
</dl>


</div>
</div>
<div class="chapter-level-extent" id="See-Also">
<h2 class="chapter">4 See Also</h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="libavformat.html">libavformat</a>
</p>

</div>
<div class="chapter-level-extent" id="Authors">
<h2 class="chapter">5 Authors</h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(git://source.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="http://source.ffmpeg.org">http://source.ffmpeg.org</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

</div>
</div>



</body>
</html>
