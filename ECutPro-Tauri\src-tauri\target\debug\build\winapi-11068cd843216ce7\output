cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="cfg"
cargo:rustc-cfg=feature="rpc"
cargo:rustc-cfg=feature="cfgmgr32"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="minwinbase"
cargo:rustc-cfg=feature="reason"
cargo:rustc-cfg=feature="rpcdce"
cargo:rustc-cfg=feature="objidlbase"
cargo:rustc-cfg=feature="rpcndr"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="wtypes"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="windef"
cargo:rustc-cfg=feature="combaseapi"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-cfg=feature="fileapi"
cargo:rustc-cfg=feature="winreg"
cargo:rustc-cfg=feature="wingdi"
cargo:rustc-cfg=feature="libloaderapi"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="processthreadsapi"
cargo:rustc-cfg=feature="objidl"
cargo:rustc-cfg=feature="shtypes"
cargo:rustc-cfg=feature="unknwnbase"
cargo:rustc-cfg=feature="oaidl"
cargo:rustc-cfg=feature="devpropdef"
cargo:rustc-cfg=feature="vadefs"
cargo:rustc-cfg=feature="winnt"
cargo:rustc-cfg=feature="wtypesbase"
cargo:rustc-cfg=feature="propidl"
cargo:rustc-link-lib=dylib=advapi32
cargo:rustc-link-lib=dylib=cfgmgr32
cargo:rustc-link-lib=dylib=gdi32
cargo:rustc-link-lib=dylib=kernel32
cargo:rustc-link-lib=dylib=msimg32
cargo:rustc-link-lib=dylib=ole32
cargo:rustc-link-lib=dylib=opengl32
cargo:rustc-link-lib=dylib=shell32
cargo:rustc-link-lib=dylib=user32
cargo:rustc-link-lib=dylib=winspool
