
use crate::models::effect_params::{EffectParam, EffectCommandGenerator};
use crate::models::ProcessingOptions;

/// FFmpeg命令生成器 - 统一负责所有FFmpeg相关逻辑
pub struct FFmpegCommandBuilder {
    input_path: String,
    output_path: String,
    hwaccel_options: Vec<String>,  // 硬件加速参数（-i之前）
    output_options: Vec<String>,   // 输出参数（-i之后）
    filters: Vec<String>,
}

/// 构建FFmpeg命令
impl FFmpegCommandBuilder {
    /// 创建FFmpeg命令构建器 - 统一处理所有配置
    pub fn new(
        input_path: &str,
        output_dir: &str,
        options: &ProcessingOptions,
        effects: &[EffectParam],
        file_index: usize
    ) -> Result<Self, String> {
        // 生成输出路径
        let output_path = crate::ffmpeg::options_config::OptionsConfigManager::generate_output_path(
            output_dir,
            input_path,
            options.naming_rule_index,
            options.output_format_index,
            file_index
        );

        // 创建命令构建器
        let mut builder = Self {
            input_path: input_path.to_string(),
            output_path,
            hwaccel_options: Vec::new(),
            output_options: Vec::new(),
            filters: Vec::new(),
        };

        // 应用处理选项
        crate::ffmpeg::options_config::OptionsConfigManager::apply_processing_options(
            &mut builder,
            options,
            Some(input_path)
        );

        // 添加效果
        builder.add_effects(effects)?;

        Ok(builder)
    }

    /// 获取输出路径
    pub fn output_path(&self) -> &str {
        &self.output_path
    }
    

    /// 添加输出选项
    pub fn output_options(&mut self, options: &[&str]) -> &mut Self {
        self.output_options.extend(options.iter().map(|s| s.to_string()));
        self
    }
    
    /// 添加效果过滤器 - 自动组合多个效果
    pub fn add_effects(&mut self, effects: &[EffectParam]) -> Result<&mut Self, String> {
        for effect in effects {
            let filter = EffectCommandGenerator::generate_filter(effect)
                .map_err(|e| format!("效果滤镜构建失败: {}", e))?;
            self.filters.push(filter);
        }
        Ok(self)
    }

    /// 检查是否已经设置了某个选项
    pub fn has_option(&self, option: &str) -> bool {
        self.output_options.iter().any(|opt| opt == option)
    }

    /// 添加硬件加速参数 - 扁平化风格，直接存储到正确位置
    pub fn add_hwaccel_options(&mut self, hwaccel: &str, device: Option<&str>, output_format: Option<&str>) -> &mut Self {
        // 直接添加到硬件加速选项中
        self.hwaccel_options.push("-hwaccel".to_string());
        self.hwaccel_options.push(hwaccel.to_string());

        if let Some(device_id) = device {
            self.hwaccel_options.push("-hwaccel_device".to_string());
            self.hwaccel_options.push(device_id.to_string());
        }
        
        if let Some(format) = output_format {
            self.hwaccel_options.push("-hwaccel_output_format".to_string());
            self.hwaccel_options.push(format.to_string());
        }

        self
    }
    
    /// 构建最终命令 - 扁平化风格，简洁明了
    pub fn build(&self) -> Vec<String> {
        let mut cmd = vec!["ffmpeg".to_string()];

        // 1. 硬件加速参数（-i之前）
        cmd.extend(self.hwaccel_options.clone());

        // 2. 输入文件
        cmd.push("-i".to_string());
        cmd.push(self.input_path.clone());

        // 3. 过滤器
        if !self.filters.is_empty() {
            cmd.push("-filter_complex".to_string());
            cmd.push(self.filters.join(","));
        }

        // 4. 输出选项
        cmd.extend(self.output_options.clone());

        // 5. 覆盖输出文件
        cmd.push("-y".to_string());
        cmd.push(self.output_path.clone());

        cmd
    }
}

