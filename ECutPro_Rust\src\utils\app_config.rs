use std::path::PathBuf;
use std::sync::OnceLock;


/// 应用程序配置
pub struct AppConfig {
    /// FFmpeg二进制文件路径
    pub ffmpeg_path: PathBuf,
    
    /// 临时文件目录
    pub temp_dir: PathBuf,
}

/// 全局配置实例
static CONFIG: OnceLock<AppConfig> = OnceLock::new();

/// 初始化配置
pub fn init_config() -> &'static AppConfig {
    CONFIG.get_or_init(|| {
        // 获取项目根目录，失败时使用当前目录
        let project_root = std::env::current_dir().unwrap_or_else(|err| {
            log::warn!("无法获取当前目录: {}，使用默认路径", err);
            PathBuf::from(".")
        });
        let ffmpeg_bin_dir = project_root.join("Resources")
                                        .join("FFmpeg")
                                        .join("Windows")
                                        .join("ffmpeg")
                                        .join("bin");
        
        // 设置ffmpeg可执行文件路径
        let ffmpeg_path = ffmpeg_bin_dir.join("ffmpeg.exe");

        // 验证FFmpeg是否存在（启动时验证，避免运行时重复检查）
        if !ffmpeg_path.exists() {
            panic!("FFmpeg not found at: {:?}. Please ensure FFmpeg is properly installed.", ffmpeg_path);
        }

        // 创建临时目录 - 在当前目录下
        let temp_dir = project_root.join("temp");
        std::fs::create_dir_all(&temp_dir).unwrap_or_else(|err| {
            log::warn!("无法创建临时目录 {:?}: {}，程序可能无法正常工作", temp_dir, err);
        });

        log::info!("FFmpeg验证成功: {:?}", ffmpeg_path);

        AppConfig {
            ffmpeg_path,
            temp_dir,
        }
    })
}

/// 获取配置
pub fn get_config() -> &'static AppConfig {
    CONFIG.get().unwrap_or_else(|| {
        log::info!("首次获取配置，正在初始化...");
        init_config()
    })
}

/// 清理所有临时文件和目录
///
/// 清理临时目录中的所有文件和子目录，释放磁盘空间
/// 这是一个通用的清理函数，可以在程序退出时调用
pub fn clear_temp_files() {
    let config = get_config();

    if let Ok(entries) = std::fs::read_dir(&config.temp_dir) {
        for entry in entries.flatten() {
            let path = entry.path();

            if let Ok(metadata) = entry.metadata() {
                if metadata.is_file() {
                    // 删除文件
                    if let Err(e) = std::fs::remove_file(&path) {
                        log::warn!("删除临时文件失败: {:?} - {}", path, e);
                    }
                } else if metadata.is_dir() {
                    // 删除目录及其内容
                    if let Err(e) = std::fs::remove_dir_all(&path) {
                        log::warn!("删除临时目录失败: {:?} - {}", path, e);
                    }
                }
            }
        }
    }

    log::info!("🧹 临时文件和目录已清理");
}




