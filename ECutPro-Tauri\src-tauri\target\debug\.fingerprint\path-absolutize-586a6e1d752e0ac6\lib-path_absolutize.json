{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"lazy_static_cache\", \"once_cell_cache\", \"unsafe_cache\", \"use_unix_paths_on_wasm\"]", "target": 13396462866987690293, "profile": 2241668132362809309, "path": 8116387835660363397, "deps": [[15076090926331863951, "path_dedot", false, 2373941512212241614]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\path-absolutize-586a6e1d752e0ac6\\dep-lib-path_absolutize", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}