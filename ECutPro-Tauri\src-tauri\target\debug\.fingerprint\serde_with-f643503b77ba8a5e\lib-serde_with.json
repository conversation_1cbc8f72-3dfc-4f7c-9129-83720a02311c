{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 511476171623209585, "path": 11860637318952268575, "deps": [[7026957619838884710, "serde_with_macros", false, 7902326344938640698], [9689903380558560274, "serde", false, 1353406847960051471], [16257276029081467297, "serde_derive", false, 3461680301453841435]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-f643503b77ba8a5e\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}