/// 前端参数验证工具
/// 
/// 专门负责验证前端传来的各种配置参数，确保它们在有效范围内。
/// 这个模块将所有验证逻辑集中管理，避免在其他地方散布验证代码。

use crate::models::ProcessingOptions;

/// 前端参数验证器
pub struct FrontendValidator;

impl FrontendValidator {
    /// 验证处理选项配置参数
    /// 
    /// # 参数
    /// * `options` - 前端传来的处理选项
    /// 
    /// # 返回
    /// * `Ok(())` - 所有参数都有效
    /// * `Err(String)` - 包含具体的无效参数信息
    pub fn validate_processing_options(options: &ProcessingOptions) -> Result<(), String> {
        // 验证输出分辨率索引 (0-4)
        if !(0..=4).contains(&options.output_resolution_index) {
            return Err(format!("invalid_output_resolution_index: {}", options.output_resolution_index));
        }
        
        // 验证输出质量索引 (0-4)
        if !(0..=4).contains(&options.output_quality_index) {
            return Err(format!("invalid_output_quality_index: {}", options.output_quality_index));
        }
        
        // 验证删除原文件索引 (0-1)
        if !(0..=1).contains(&options.delete_original_index) {
            return Err(format!("invalid_delete_original_index: {}", options.delete_original_index));
        }
        
        // 验证命名规则索引 (0-2)
        if !(0..=2).contains(&options.naming_rule_index) {
            return Err(format!("invalid_naming_rule_index: {}", options.naming_rule_index));
        }
        
        // 验证线程数索引 (>= 0)
        if options.thread_count_index < 0 {
            return Err(format!("invalid_thread_count_index: {}", options.thread_count_index));
        }
        
        // 验证GPU加速索引 (0-3)
        if !(0..=3).contains(&options.gpu_acceleration_index) {
            return Err(format!("invalid_gpu_acceleration_index: {}", options.gpu_acceleration_index));
        }
        
        // 验证输出格式索引 (0-3，移除了ProRes)
        if !(0..=3).contains(&options.output_format_index) {
            return Err(format!("invalid_output_format_index: {}", options.output_format_index));
        }
        
        Ok(())
    }

}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::ProcessingOptions;

    #[test]
    fn test_valid_processing_options() {
        let options = ProcessingOptions {
            output_resolution_index: 1,
            output_quality_index: 2,
            delete_original_index: 0,
            naming_rule_index: 1,
            thread_count_index: 4,
            gpu_acceleration_index: 1,
            output_format_index: 1,
            id: None,
            output_path: Some("test".to_string()),
        };
        
        assert!(FrontendValidator::validate_processing_options(&options).is_ok());
    }

    #[test]
    fn test_invalid_resolution_index() {
        let options = ProcessingOptions {
            output_resolution_index: 5, // 无效值
            ..Default::default()
        };

        let result = FrontendValidator::validate_processing_options(&options);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("invalid_output_resolution_index"));
    }

    #[test]
    fn test_invalid_format_index() {
        let options = ProcessingOptions {
            output_format_index: 4, // 无效值 (ProRes已移除)
            ..Default::default()
        };

        let result = FrontendValidator::validate_processing_options(&options);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("invalid_output_format_index"));
    }

    #[test]
    fn test_invalid_gpu_index() {
        let options = ProcessingOptions {
            gpu_acceleration_index: -1, // 无效值
            ..Default::default()
        };

        let result = FrontendValidator::validate_processing_options(&options);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("invalid_gpu_acceleration_index"));
    }

    #[test]
    fn test_invalid_thread_count() {
        let options = ProcessingOptions {
            thread_count_index: -5, // 无效值
            ..Default::default()
        };

        let result = FrontendValidator::validate_processing_options(&options);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("invalid_thread_count_index"));
    }
}
