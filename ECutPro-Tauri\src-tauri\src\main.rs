// ******************************************************
// E剪Pro - Tauri应用入口
//
// 设计理念：
//   1. 拥抱Tauri架构 - 完全基于命令和事件
//   2. 服务注入模式 - 通过Tauri状态管理依赖
//   3. 优雅的错误处理 - 统一的Result类型
//   4. 清晰的模块分离 - 每个模块职责单一
//   5. 现代Rust实践 - 充分利用类型系统
// ******************************************************

// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod ffmpeg;
mod models;
mod services;
mod utils;
mod tauri_helpers;
mod tauri_helpers;

use log::info;
use tauri::Manager;
use services::{VideoProcessingService, VideoAnalysisService, VideoPreviewService};
use utils::get_config;
use tauri_helpers::TauriSender;

/// Tauri应用入口点
///
/// 采用现代Tauri架构：
/// - 命令驱动：所有业务逻辑通过commands暴露
/// - 事件推送：实时状态通过Tauri事件系统推送
/// - 状态管理：服务实例通过Tauri状态管理
/// - 优雅关闭：依赖Tauri生命周期管理
fn main() {
    // 初始化日志系统
    env_logger::init_from_env(
        env_logger::Env::default().default_filter_or("info")
    );

    // 加载应用配置
    let _config = get_config();
    info!("🚀 E剪Pro正在启动...");

    // 构建Tauri应用
    tauri::Builder::default()
        // 注册服务到Tauri状态管理
        .setup(|app| {
            let app_handle = app.handle();

            // 创建Tauri发送器
            let tauri_sender = TauriSender::new(app_handle.clone());

            // 创建核心服务实例（保持你的完美架构）
            let video_service = VideoProcessingService::new(tauri_sender.clone());
            let analysis_service = VideoAnalysisService::new(app_handle.clone());
            let preview_service = VideoPreviewService::new(app_handle.clone());

            // 注入到Tauri状态管理
            app.manage(video_service);
            app.manage(analysis_service);
            app.manage(preview_service);

            info!("✅ 所有服务已初始化并注入状态管理");
            Ok(())
        })
        // 注册所有Tauri命令
        .invoke_handler(tauri::generate_handler![
            commands::process_videos,
            commands::analyze_video,
            commands::get_gpu_info,
            commands::cancel_all_tasks,
        ])
        // 启动应用
        .run(tauri::generate_context!())
        .expect("❌ Tauri应用启动失败");
}