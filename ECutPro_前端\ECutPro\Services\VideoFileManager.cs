// ******************************************************
// 文件名: VideoFileManager.cs
// 功能描述: 视频文件管理服务 - 纯文件管理，学习Rust的简洁风格
// 主要职责: 
//   1. 管理视频文件列表
//   2. 文件添加、删除、清空操作
//   3. 文件状态管理
// ******************************************************

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using ECutPro.Models;

namespace ECutPro.Services
{
    /// <summary>
    /// 视频文件管理服务 - 单一职责：文件管理
    /// </summary>
    public sealed class VideoFileManager
    {
        #region 属性
        
        /// <summary>
        /// 视频文件列表
        /// </summary>
        public ObservableCollection<VideoFileInfo> Files { get; } = new();
        
        /// <summary>
        /// 是否有文件
        /// </summary>
        public bool HasFiles => Files.Count > 0;
        
        /// <summary>
        /// 文件总数
        /// </summary>
        public int FileCount => Files.Count;
        
        #endregion
        
        #region 文件操作
        
        /// <summary>
        /// 添加视频文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        public void AddFiles(IEnumerable<string> filePaths)
        {
            if (filePaths == null) return;
            
            foreach (var filePath in filePaths)
            {
                if (string.IsNullOrWhiteSpace(filePath)) continue;
                if (!File.Exists(filePath)) continue;
                if (Files.Any(f => f.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase))) continue;
                
                var fileInfo = CreateVideoFileInfo(filePath);
                Files.Add(fileInfo);
            }
        }
        
        /// <summary>
        /// 添加单个视频文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public void AddFile(string filePath)
        {
            AddFiles(new[] { filePath });
        }
        
        /// <summary>
        /// 移除指定索引的文件
        /// </summary>
        /// <param name="index">文件索引</param>
        public void RemoveFile(int index)
        {
            if (index >= 0 && index < Files.Count)
            {
                Files.RemoveAt(index);
            }
        }
        
        /// <summary>
        /// 移除指定文件
        /// </summary>
        /// <param name="fileInfo">文件信息</param>
        public void RemoveFile(VideoFileInfo fileInfo)
        {
            if (fileInfo != null)
            {
                Files.Remove(fileInfo);
            }
        }
        
        /// <summary>
        /// 清空所有文件
        /// </summary>
        public void Clear()
        {
            Files.Clear();
        }
        
        /// <summary>
        /// 获取所有文件路径
        /// </summary>
        /// <returns>文件路径列表</returns>
        public List<string> GetFilePaths()
        {
            return Files.Select(f => f.FilePath).ToList();
        }
        
        /// <summary>
        /// 获取有效的文件路径（文件存在）
        /// </summary>
        /// <returns>有效文件路径列表</returns>
        public List<string> GetValidFilePaths()
        {
            return Files
                .Where(f => File.Exists(f.FilePath))
                .Select(f => f.FilePath)
                .ToList();
        }
        
        #endregion
        
        #region 文件状态管理
        
        /// <summary>
        /// 更新文件处理状态
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="status">处理状态</param>
        public void UpdateFileStatus(string filePath, string status)
        {
            var file = Files.FirstOrDefault(f => f.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase));
            if (file != null)
            {
                file.Status = status;
            }
        }

        /// <summary>
        /// 批量取消所有待处理和处理中的任务
        /// </summary>
        public void CancelAllPendingTasks()
        {
            foreach (var file in Files)
            {
                if (file.Status == "待处理" || file.Status.StartsWith("处理中"))
                {
                    file.Status = "已取消";
                }
            }
        }
        


        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 创建视频文件信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>视频文件信息</returns>
        private static VideoFileInfo CreateVideoFileInfo(string filePath)
        {
            // 使用VideoFileInfo的构造函数，自动设置默认状态
            return new VideoFileInfo(filePath);
        }

        #endregion
    }
}
