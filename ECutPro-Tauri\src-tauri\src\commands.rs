// ******************************************************
// Tauri命令层 - 纯净的前后端交互接口
//
// 核心理念：
//   1. 保留完美的业务逻辑 - 只改变交互方式
//   2. 直接的函数调用 - 抛弃消息包装思维
//   3. 原生类型安全 - 利用Rust+TypeScript类型系统
//   4. 事件驱动进度 - 用Tauri事件替代TCP推送
//   5. 简洁优雅 - 最少的抽象层
// ******************************************************

use tauri::{command, AppHandle, Manager};
use log::{info, error, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::models::{EffectParam, ProcessingOptions};
use crate::services::{VideoProcessingService, VideoAnalysisService};
use crate::ffmpeg::gpu_config::detect_gpu_acceleration;

/// 统一的命令结果类型 - 简单直接
type CommandResult<T> = Result<T, String>;

/// 视频文件信息 - 前端需要的最小数据集
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoInfo {
    pub path: String,
    pub name: String,
    pub duration: f64,
    pub width: u32,
    pub height: u32,
    pub fps: f64,
    pub codec: String,
    pub size_mb: f64,
}

/// 处理进度信息 - 实时推送给前端
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessProgress {
    pub task_id: String,
    pub current_file: String,
    pub file_progress: f64,      // 当前文件进度 0-100
    pub total_progress: f64,     // 总体进度 0-100
    pub files_completed: usize,
    pub files_total: usize,
    pub status: String,          // "processing" | "completed" | "failed" | "cancelled"
}

/// GPU加速信息 - 系统检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GpuInfo {
    pub nvidia_available: bool,
    pub amd_available: bool,
    pub intel_available: bool,
    pub recommended: String,     // 推荐使用的加速方式
}

/// 启动视频批量处理 - 调用你的完美队列架构
///
/// 保持你的完美设计：队列驱动、多线程、智能调度
/// 只改变通信方式：TCP → Tauri事件
///
/// # 参数
/// * `files` - 视频文件路径列表
/// * `output_dir` - 输出目录
/// * `effects` - 效果参数（保持你原有的完美设计）
/// * `options` - 处理选项（保持你原有的完美设计）
/// * `video_service` - 注入的视频处理服务
///
/// # 返回
/// * `Ok(task_id)` - 任务ID，前端用于跟踪
/// * `Err(message)` - 启动失败原因
#[command]
pub async fn process_videos(
    files: Vec<String>,
    output_dir: String,
    effects: Vec<EffectParam>,
    options: ProcessingOptions,
    video_service: tauri::State<'_, VideoProcessingService>,
) -> CommandResult<String> {
    info!("🎬 启动视频处理: {} 个文件 → {}", files.len(), output_dir);

    // 构建ProcessRequest（保持你的原有接口）
    let mut request = ProcessRequest {
        input_paths: files,
        output_dir,
        effects,
        options,
    };

    // 调用你的完美队列架构
    match video_service.process_videos(request).await {
        Ok(task_id) => {
            info!("✅ 视频处理任务已启动: {}", task_id);
            Ok(task_id)
        }
        Err(e) => {
            error!("❌ 启动视频处理失败: {}", e);
            Err(format!("处理启动失败: {}", e))
        }
    }
}

/// 分析视频文件 - 直接调用你的分析逻辑
///
/// # 参数
/// * `file_path` - 视频文件路径
///
/// # 返回
/// * `Ok(VideoInfo)` - 视频信息
/// * `Err(message)` - 分析失败原因
#[command]
pub async fn analyze_video(file_path: String) -> CommandResult<VideoInfo> {
    info!("🔍 分析视频: {}", file_path);

    // 直接调用你的完美分析逻辑
    let service = VideoAnalysisService::new();

    match service.analyze_video(&file_path).await {
        Ok(metadata) => {
            info!("✅ 分析完成: {}", file_path);

            // 转换为前端需要的格式
            let video_info = VideoInfo {
                path: file_path.clone(),
                name: std::path::Path::new(&file_path)
                    .file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string(),
                duration: metadata.get("duration").and_then(|v| v.as_f64()).unwrap_or(0.0),
                width: metadata.get("width").and_then(|v| v.as_u64()).unwrap_or(0) as u32,
                height: metadata.get("height").and_then(|v| v.as_u64()).unwrap_or(0) as u32,
                fps: metadata.get("fps").and_then(|v| v.as_f64()).unwrap_or(0.0),
                codec: metadata.get("codec").and_then(|v| v.as_str()).unwrap_or("unknown").to_string(),
                size_mb: metadata.get("size_mb").and_then(|v| v.as_f64()).unwrap_or(0.0),
            };

            Ok(video_info)
        }
        Err(e) => {
            error!("❌ 分析失败: {} - {}", file_path, e);
            Err(format!("分析失败: {}", e))
        }
    }
}

/// 检测GPU加速支持 - 直接调用你的检测逻辑
///
/// # 返回
/// * `Ok(GpuInfo)` - GPU支持信息
/// * `Err(message)` - 检测失败原因
#[command]
pub async fn get_gpu_info() -> CommandResult<GpuInfo> {
    info!("🎮 检测GPU加速支持...");

    // 直接调用你的完美GPU检测逻辑
    match detect_gpu_acceleration() {
        Ok(detection_result) => {
            let gpu_info = GpuInfo {
                nvidia_available: detection_result.nvidia_available,
                amd_available: detection_result.amd_available,
                intel_available: detection_result.intel_available,
                recommended: detection_result.recommended_acceleration,
            };

            info!("✅ GPU检测完成: {:?}", gpu_info);
            Ok(gpu_info)
        }
        Err(e) => {
            warn!("⚠️ GPU检测失败: {}", e);
            // 返回默认值，不阻塞应用
            Ok(GpuInfo {
                nvidia_available: false,
                amd_available: false,
                intel_available: false,
                recommended: "software".to_string(),
            })
        }
    }
}

/// 取消所有处理任务 - 调用你的完美全局取消机制
///
/// # 参数
/// * `video_service` - 注入的视频处理服务
///
/// # 返回
/// * `Ok(())` - 取消成功
/// * `Err(message)` - 取消失败原因
#[command]
pub async fn cancel_all_tasks(
    video_service: tauri::State<'_, VideoProcessingService>,
) -> CommandResult<()> {
    info!("🛑 取消所有任务");

    // 调用你的完美全局取消逻辑
    video_service.cancel_all_tasks().await
        .map_err(|e| format!("取消失败: {}", e))?;

    info!("✅ 所有任务已取消");
    Ok(())
}

/// 获取系统信息 - 用于前端显示
///
/// # 返回
/// * `Ok(info)` - 系统信息
#[command]
pub async fn get_system_info() -> CommandResult<HashMap<String, String>> {
    let mut info = HashMap::new();

    info.insert("cpu_cores".to_string(), num_cpus::get().to_string());
    info.insert("platform".to_string(), std::env::consts::OS.to_string());
    info.insert("arch".to_string(), std::env::consts::ARCH.to_string());

    Ok(info)
}
