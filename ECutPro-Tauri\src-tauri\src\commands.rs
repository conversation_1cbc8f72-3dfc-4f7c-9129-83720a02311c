// ******************************************************
// Tauri命令层 - 纯净的前后端交互接口
//
// 核心理念：
//   1. 保留完美的业务逻辑 - 只改变交互方式
//   2. 直接的函数调用 - 抛弃消息包装思维
//   3. 原生类型安全 - 利用Rust+TypeScript类型系统
//   4. 事件驱动进度 - 用Tauri事件替代TCP推送
//   5. 简洁优雅 - 最少的抽象层
// ******************************************************

use tauri::{command, State};
use log::{info, error};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use serde_json::Value;

use crate::models::{ProcessRequest, EffectParam, ProcessingOptions};
use crate::services::{VideoProcessingService, VideoAnalysisService};
use crate::ffmpeg::gpu_config::detect_gpu_acceleration;

/// 统一的命令结果类型 - 简单直接
type CommandResult<T> = Result<T, String>;

/// 视频文件信息 - 前端需要的最小数据集
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoInfo {
    pub path: String,
    pub name: String,
    pub duration: f64,
    pub width: u32,
    pub height: u32,
    pub fps: f64,
    pub codec: String,
    pub size_mb: f64,
}

/// 处理进度信息 - 实时推送给前端
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessProgress {
    pub task_id: String,
    pub current_file: String,
    pub file_progress: f64,      // 当前文件进度 0-100
    pub total_progress: f64,     // 总体进度 0-100
    pub files_completed: usize,
    pub files_total: usize,
    pub status: String,          // "processing" | "completed" | "failed" | "cancelled"
}

/// GPU加速信息 - 系统检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GpuInfo {
    pub nvidia_available: bool,
    pub amd_available: bool,
    pub intel_available: bool,
    pub recommended: String,     // 推荐使用的加速方式
}

/// 启动视频批量处理 - Commands（请求响应）
///
/// 保持你的完美设计：队列驱动、多线程、智能调度
/// 进度通过Events实时推送，这里只返回任务ID
///
/// # 参数
/// * `files` - 视频文件路径列表
/// * `output_dir` - 输出目录
/// * `effects` - 效果参数（保持你原有的完美设计）
/// * `options` - 处理选项（保持你原有的完美设计）
/// * `video_service` - 注入的视频处理服务
///
/// # 返回
/// * `Ok(task_id)` - 任务ID，前端用于跟踪
/// * `Err(message)` - 启动失败原因
#[command]
pub async fn process_videos(
    files: Vec<String>,
    output_dir: String,
    effects: Vec<EffectParam>,
    options: ProcessingOptions,
    video_service: State<'_, VideoProcessingService>,
) -> CommandResult<String> {
    info!("🎬 启动视频处理: {} 个文件 → {}", files.len(), output_dir);

    // 构建ProcessRequest（保持你的原有接口）
    let mut request = ProcessRequest {
        input_paths: files,
        output_dir,
        effects,
        options,
    };

    // 调用你的完美队列架构
    match video_service.process_videos(request).await {
        Ok(task_id) => {
            info!("✅ 视频处理任务已启动: {}", task_id);
            Ok(task_id)
        }
        Err(e) => {
            error!("❌ 启动视频处理失败: {}", e);
            Err(format!("处理启动失败: {}", e))
        }
    }
}

/// 分析视频文件 - Commands（请求响应）
///
/// 一次性操作，直接返回分析结果，不需要推送
///
/// # 参数
/// * `file_path` - 视频文件路径
/// * `analysis_service` - 注入的分析服务
///
/// # 返回
/// * `Ok(metadata)` - 视频元数据JSON
/// * `Err(message)` - 分析失败原因
#[command]
pub async fn analyze_video(
    file_path: String,
    analysis_service: State<'_, VideoAnalysisService>,
) -> CommandResult<Value> {
    info!("🔍 分析视频: {}", file_path);

    // 直接调用你的完美分析逻辑
    match analysis_service.analyze_video(&file_path).await {
        Ok(metadata) => {
            info!("✅ 分析完成: {}", file_path);
            Ok(metadata)
        }
        Err(e) => {
            error!("❌ 分析失败: {} - {}", file_path, e);
            Err(format!("分析失败: {}", e))
        }
    }
}

/// 检测GPU加速支持 - Commands（请求响应）
///
/// 一次性检测，直接返回结果，不需要推送
///
/// # 返回
/// * `Ok(gpu_info)` - GPU支持信息JSON
/// * `Err(message)` - 检测失败原因
#[command]
pub async fn get_gpu_info() -> CommandResult<Value> {
    info!("🎮 检测GPU加速支持...");

    // 直接调用你的完美GPU检测逻辑
    match detect_gpu_acceleration() {
        Ok(detection_result) => {
            let gpu_info = serde_json::json!({
                "nvidia_available": detection_result.nvidia_available,
                "amd_available": detection_result.amd_available,
                "intel_available": detection_result.intel_available,
                "recommended": detection_result.recommended_acceleration
            });

            info!("✅ GPU检测完成");
            Ok(gpu_info)
        }
        Err(e) => {
            error!("❌ GPU检测失败: {}", e);
            Err(format!("GPU检测失败: {}", e))
        }
    }
}

/// 取消所有处理任务 - Commands（请求响应）
///
/// 一次性操作，执行取消并返回结果
///
/// # 参数
/// * `video_service` - 注入的视频处理服务
///
/// # 返回
/// * `Ok(())` - 取消成功
/// * `Err(message)` - 取消失败原因
#[command]
pub async fn cancel_all_tasks(
    video_service: State<'_, VideoProcessingService>,
) -> CommandResult<()> {
    info!("🛑 取消所有任务");

    // 调用你的完美全局取消逻辑
    video_service.cancel_all_tasks().await
        .map_err(|e| format!("取消失败: {}", e))?;

    info!("✅ 所有任务已取消");
    Ok(())
}

/// 获取系统信息 - 用于前端显示
///
/// # 返回
/// * `Ok(info)` - 系统信息
#[command]
pub async fn get_system_info() -> CommandResult<HashMap<String, String>> {
    let mut info = HashMap::new();

    info.insert("cpu_cores".to_string(), num_cpus::get().to_string());
    info.insert("platform".to_string(), std::env::consts::OS.to_string());
    info.insert("arch".to_string(), std::env::consts::ARCH.to_string());

    Ok(info)
}
