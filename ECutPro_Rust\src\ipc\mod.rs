// ******************************************************
// 文件名: mod.rs
// 功能描述: 进程间通信(IPC)模块
// 主要职责: 
//   1. 定义消息类型和结构
//   2. 提供命名管道服务器实现
//   3. 处理消息序列化和发送
// ******************************************************

// mod pipe_server;        // 原版本 - 命名管道实现（暂时禁用，缺少依赖）
mod tcp_duplex_server;  // 标准TCP全双工实现 ⭐ 推荐
mod message_handler;

// pub use pipe_server::PipeServer;                    // 原版本（暂时禁用）
pub use tcp_duplex_server::TcpDuplexServer;         // 标准TCP全双工 ⭐ 推荐
pub use message_handler::{MessageHandler, Command};

use serde::{Deserialize, Serialize};

/// 消息类型枚举 - 统一为UniversalMessage
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum Message {
    /// 视频处理消息（进度、状态等）
    VideoProcessing(UniversalMessage),

    /// 预览结果消息
    Preview(UniversalMessage),

    /// GPU检查结果消息
    GpuCheck(UniversalMessage),

    /// 视频分析结果消息
    VideoAnalysis(UniversalMessage),
}



/// 通用消息结构 - 所有消息都使用这个结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UniversalMessage {
    /// 是否成功
    pub success: bool,

    /// 数据载荷（JSON格式，可以包含任何数据）
    pub data: serde_json::Value,

    /// 用户友好的消息（成功时可选，失败时必需）
    pub message: Option<String>,
}



// ============================================================================
// 统一消息构造函数 - 所有服务只使用这两个函数！
// ============================================================================

/// 创建成功消息 - 统一全局函数
///
/// 所有服务的成功响应都使用这个函数
///
/// # 参数
/// * `data` - 任意JSON数据
///
/// # 示例
/// ```
/// // 视频处理进度
/// success(json!({"task_id": "video1.mp4", "progress": "处理中 (45%)", "global_progress": 67.5}))
///
/// // 预览结果
/// success(json!({"frames_directory": "./temp/frames/"}))
///
/// // 分析结果
/// success(json!({"video_path": "video.mp4", "metadata": metadata}))
/// ```
pub fn success(data: serde_json::Value) -> UniversalMessage {
    UniversalMessage {
        success: true,
        data,
        message: None,
    }
}

/// 创建失败消息 - 统一全局函数
///
/// 所有服务的失败响应都使用这个函数
///
/// # 参数
/// * `message` - 用户友好的错误信息
///
/// # 示例
/// ```
/// // 视频处理失败
/// error("视频处理失败，请检查文件格式".to_string())
///
/// // 预览失败
/// error("预览生成失败，请稍后重试".to_string())
///
/// // 分析失败
/// error("无法分析视频文件，请确认文件格式是否受支持".to_string())
/// ```
pub fn error(message: String) -> UniversalMessage {
    UniversalMessage {
        success: false,
        data: serde_json::Value::Null,
        message: Some(message),
    }
}