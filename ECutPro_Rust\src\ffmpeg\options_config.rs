use crate::ffmpeg::FFmpegCommandBuilder;
use crate::models::ProcessingOptions;

/// 前端选项配置管理器 - 专门处理前端UI选项到FFmpeg参数的映射
pub struct OptionsConfigManager;

impl OptionsConfigManager {
    /// 应用前端处理选项到FFmpeg命令构建器
    /// 
    /// 此方法根据用户在前端选择的处理选项配置FFmpeg命令，包括：
    /// 1. 输出分辨率设置
    /// 2. 输出格式和编码器设置
    /// 3. 质量参数设置
    /// 4. 音频流设置
    /// 
    /// 注意：GPU加速相关设置由 GpuConfigManager 单独处理
    /// 
    /// # 参数
    /// * `cmd_builder` - FFmpeg命令构建器
    /// * `options` - 前端传来的处理选项
    /// * `input_path` - 输入文件路径，用于获取原视频信息
    pub fn apply_processing_options(
        cmd_builder: &mut FFmpegCommandBuilder, 
        options: &ProcessingOptions, 
        input_path: Option<&str>
    ) {
        // 应用分辨率设置
        Self::apply_resolution_settings(cmd_builder, options);
        
        // 应用编码设置（仅在未启用GPU加速时）
        if options.gpu_acceleration_index == 0 {
            Self::apply_cpu_encoding_settings(cmd_builder, options, input_path);
        }
        
        // 应用音频设置
        Self::apply_audio_settings(cmd_builder);

        // 应用GPU设置（如果启用）
        if options.gpu_acceleration_index > 0 {
            let has_bitrate = cmd_builder.has_option("-b:v");
            crate::ffmpeg::gpu_config::GpuConfigManager::apply_gpu_config(
                cmd_builder,
                options,
                has_bitrate,
                input_path
            );
        }
    }

    /// 应用分辨率设置 - 扁平化风格
    fn apply_resolution_settings(cmd_builder: &mut FFmpegCommandBuilder, options: &ProcessingOptions) {
        match options.output_resolution_index {
            0 => {}, // 保持原分辨率
            1 => { cmd_builder.output_options(&["-s", "1920x1080"]); }, // 1080p横屏
            2 => { cmd_builder.output_options(&["-s", "1280x720"]); },  // 720p横屏
            3 => { cmd_builder.output_options(&["-s", "1080x1920"]); }, // 1080p竖屏
            4 => { cmd_builder.output_options(&["-s", "720x1280"]); },  // 720p竖屏
            _ => {} // 默认保持原分辨率
        }
    }

    /// 应用CPU编码设置 - 扁平化风格
    fn apply_cpu_encoding_settings(
        cmd_builder: &mut FFmpegCommandBuilder, 
        options: &ProcessingOptions, 
        input_path: Option<&str>
    ) {
        match options.output_format_index {
            0 => {
                // 原格式，应用通用质量设置
                Self::apply_quality_settings(cmd_builder, options.output_quality_index, None, input_path);
            },
            1 => {
                // MP4格式
                cmd_builder.output_options(&["-c:v", "libx264", "-pix_fmt", "yuv420p"]);
                Self::apply_quality_settings(cmd_builder, options.output_quality_index, Some("h264"), input_path);
            },
            2 => {
                // MOV格式
                cmd_builder.output_options(&["-c:v", "libx264", "-f", "mov"]);
                Self::apply_quality_settings(cmd_builder, options.output_quality_index, Some("h264"), input_path);
            },
            3 => {
                // MKV格式
                cmd_builder.output_options(&["-c:v", "libx264", "-f", "matroska"]);
                Self::apply_quality_settings(cmd_builder, options.output_quality_index, Some("h264"), input_path);
            },
            _ => {
                // 默认处理
                Self::apply_quality_settings(cmd_builder, options.output_quality_index, None, input_path);
            }
        }
    }

    /// 应用质量设置 - 行业最佳实践版本（简化但专业）
    fn apply_quality_settings(
        cmd_builder: &mut FFmpegCommandBuilder,
        quality_index: i32,
        _codec: Option<&str>,
        input_path: Option<&str>
    ) {
        match quality_index {
            0 => {
                // 原质量：智能分析原视频质量
                let crf = Self::analyze_original_crf(input_path).unwrap_or(18);
                cmd_builder.output_options(&[
                    "-crf", &crf.to_string(),
                    "-preset", "medium",
                    "-tune", "film"  // 通用内容优化
                ]);
            },
            1 => {
                // 无损质量：H.264 CRF 0 无损编码
                // 警告：无损编码会产生非常大的文件（可能是原文件的5-20倍）
                // 适用场景：专业后期制作、质量要求极高的项目
                // 不适用：日常分享、网络传输、存储空间有限的场景
                log::info!("使用无损质量设置，输出文件将会很大，编码时间较长");
                cmd_builder.output_options(&["-crf", "0", "-preset", "veryslow"]);
            },
            2 => {
                // 高质量：视觉无损，专业级质量
                cmd_builder.output_options(&[
                    "-crf", "18",
                    "-preset", "slow",
                    "-tune", "film"
                ]);
            },
            3 => {
                // 标准质量：质量和速度的最佳平衡
                cmd_builder.output_options(&[
                    "-crf", "23",
                    "-preset", "medium",
                    "-tune", "film"
                ]);
            },
            4 => {
                // 低质量：快速编码，适合预览或快速处理
                cmd_builder.output_options(&[
                    "-crf", "28",
                    "-preset", "fast"
                ]);
            },
            _ => {
                // 默认标准质量
                cmd_builder.output_options(&["-crf", "23", "-preset", "medium", "-tune", "film"]);
            }
        }
    }

    /// 分析原视频质量，返回合适的CRF值 - 简单实用的方法
    fn analyze_original_crf(input_path: Option<&str>) -> Option<i32> {
        input_path.and_then(|path| {
            // 尝试获取原视频码率，根据码率推断合适的CRF
            crate::ffmpeg::FFmpegAnalyzer::get_bitrate(path).map(|bitrate| {
                match bitrate {
                    0..=1000 => 28,      // 低码率视频 -> 低质量CRF
                    1001..=3000 => 23,   // 中等码率 -> 标准CRF
                    3001..=8000 => 18,   // 高码率 -> 高质量CRF
                    _ => 15,             // 超高码率 -> 接近无损CRF
                }
            })
        })
    }



    /// 应用音频设置
    fn apply_audio_settings(cmd_builder: &mut FFmpegCommandBuilder) {
        // 保留原始音频流（不重新编码）
        cmd_builder.output_options(&["-c:a", "copy"]);
    }

    /// 根据格式索引获取文件扩展名 - 简洁实现
    pub fn get_extension_by_format(format_index: i32, original_path: Option<&str>) -> String {
        match format_index {
            0 => {
                // 原格式：直接获取扩展名，前端已确保文件有效
                original_path
                    .and_then(|path| std::path::Path::new(path).extension())
                    .and_then(|ext| ext.to_str())
                    .unwrap_or("mp4")
                    .to_string()
            },
            1 => "mp4".to_string(),
            2 => "mov".to_string(),
            3 => "mkv".to_string(),
            _ => "mp4".to_string(),
        }
    }

    /// 根据命名规则生成文件名 - 前端选项处理
    pub fn generate_filename(stem: &str, extension: &str, naming_rule_index: i32, file_index: usize) -> String {
        match naming_rule_index {
            0 => format!("{}.{}", stem, extension), // 原文件名
            1 => format!("{}_{}.{}", file_index, stem, extension), // 前缀序号
            2 => format!("{}_{}.{}", stem, file_index, extension), // 后缀序号
            _ => format!("{}.{}", stem, extension), // 默认使用原文件名
        }
    }

    /// 生成输出文件路径 - 前端选项综合处理
    pub fn generate_output_path(output_dir: &str, input_path: &str, naming_rule_index: i32, format_index: i32, file_index: usize) -> String {
        let input = std::path::Path::new(input_path);
        let stem = input.file_stem().unwrap().to_string_lossy();

        // 获取扩展名
        let extension = Self::get_extension_by_format(format_index, Some(input_path));

        // 生成文件名
        let filename = Self::generate_filename(&stem, &extension, naming_rule_index, file_index);

        // 组合输出路径
        let output_path = std::path::Path::new(output_dir).join(filename);
        output_path.to_string_lossy().to_string()
    }
}
