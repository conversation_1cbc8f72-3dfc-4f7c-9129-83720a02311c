use std::sync::Arc;
use log::info;
use tokio::signal;

mod ffmpeg;
mod models;
mod utils;
mod ipc;
mod services;

use utils::{get_config, clear_temp_files};
use ipc::{TcpDuplexServer, MessageHandler};
use services::{VideoProcessingService, VideoAnalysisService, VideoPreviewService};

/// 创建断连处理函数 - 新架构：全局取消
fn create_disconnect_handler() -> impl Fn() + Send + 'static {
    move || {
        info!("🔌 检测到前端断连，开始优雅停止所有任务...");

        tokio::spawn(async move {
            // 清理临时文件
            info!("🧹 清理临时文件...");
            clear_temp_files();

            info!("🚪 程序将优雅退出");
            std::process::exit(0);
        });
    }
}

#[tokio::main]
async fn main() -> std::io::Result<()> {
    // 初始化日志
    env_logger::init_from_env(env_logger::Env::default().default_filter_or("info"));
    
    // 初始化配置
    let _config = get_config();
    info!("服务器配置已加载");
    
    // 创建TCP全双工服务器
    let tcp_server = Arc::new(TcpDuplexServer::new(None, create_disconnect_handler()));
    info!("TCP全双工服务器已创建");

    // 创建视频处理服务（集成任务管理）
    let video_service = Arc::new(VideoProcessingService::new(tcp_server.clone()));
    info!("视频处理服务已初始化");

    // 创建其他服务（使用新的推送架构）
    let analysis_service = VideoAnalysisService::new(tcp_server.clone());
    let preview_service = VideoPreviewService::new(tcp_server.clone());
    info!("分析和预览服务已初始化");

    // 创建消息处理器
    let message_handler = MessageHandler::new(
        video_service,
        analysis_service,
        preview_service,
        tcp_server.clone(),
    );
    info!("消息处理器已初始化");

    // 启动命令监听器
    tcp_server.start_command_listener(move |cmd| {
        let handler = message_handler.clone();
        tokio::spawn(async move {
            handler.handle_command(cmd).await;
        });
    });
    info!("命令监听器已启动");
    
    // 等待终止信号
    info!("服务器已启动，按Ctrl+C终止...");
    signal::ctrl_c().await?;
    info!("收到终止信号，正在关闭服务器...");
    
    Ok(())
}