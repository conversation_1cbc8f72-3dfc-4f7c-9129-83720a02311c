{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5203849610196542808, "build_script_build", false, 15798795358390011710]], "local": [{"RerunIfChanged": {"output": "debug\\build\\ecutpro-tauri-65e45c9390e6a87f\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}