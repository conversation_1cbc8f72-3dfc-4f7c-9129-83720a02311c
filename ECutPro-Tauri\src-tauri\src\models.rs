// ******************************************************
// 数据模型定义 - 保持与原项目一致
// ******************************************************

use serde::{Deserialize, Serialize};

/// 视频处理请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessRequest {
    /// 输入视频路径列表
    pub input_paths: Vec<String>,
    /// 输出目录
    pub output_dir: String,
    /// 效果参数列表
    pub effects: Vec<EffectParam>,
    /// 处理选项
    pub options: ProcessingOptions,
}

/// 效果参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EffectParam {
    /// 效果类型
    pub effect_type: String,
    /// 效果参数
    pub params: serde_json::Value,
}

/// 处理选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingOptions {
    /// 输出格式
    pub output_format: String,
    /// 质量设置
    pub quality: String,
    /// GPU加速
    pub gpu_acceleration: bool,
    /// 线程数
    pub thread_count: Option<usize>,
}

impl Default for ProcessingOptions {
    fn default() -> Self {
        Self {
            output_format: "mp4".to_string(),
            quality: "high".to_string(),
            gpu_acceleration: false,
            thread_count: None,
        }
    }
}
