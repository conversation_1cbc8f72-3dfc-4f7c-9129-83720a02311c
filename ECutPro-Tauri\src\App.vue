<script setup lang="ts">
import { ref } from "vue";
import { invoke } from "@tauri-apps/api/core";

const greetMsg = ref("");
const name = ref("");

async function greet() {
  // 测试Tauri命令调用
  greetMsg.value = await invoke("greet", { name: name.value });
}
</script>

<template>
  <main class="container">
    <h1>E剪 Pro - Tauri版本</h1>
    <p>专业视频批量处理工具</p>

    <div class="test-section">
      <h2>Tauri连接测试</h2>
      <form class="row" @submit.prevent="greet">
        <input id="greet-input" v-model="name" placeholder="输入名称测试..." />
        <button type="submit">测试连接</button>
      </form>
      <p v-if="greetMsg" class="result">{{ greetMsg }}</p>
    </div>
  </main>
</template>

<style scoped>
.container {
  margin: 0;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.test-section {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem auto;
  max-width: 500px;
  backdrop-filter: blur(10px);
}

.row {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 1rem 0;
}

input,
button {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.8em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.25s;
}

input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

button {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.2);
}

button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.result {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 0, 0.3);
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
</style>