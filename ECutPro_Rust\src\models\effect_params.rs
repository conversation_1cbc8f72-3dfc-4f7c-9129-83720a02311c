use serde::{Deserialize, Serialize};
use serde_json::Value;

/// 效果参数
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct EffectParam {
    /// 效果唯一标识
    pub effect_id: String,
    
    /// 效果类型
    pub effect_type: String,
    
    /// 参数值（使用JSON对象接收任意参数）
    pub params: Value,
}

/// 效果命令生成器
/// 负责根据效果类型生成对应的FFmpeg过滤器命令
pub struct EffectCommandGenerator;

impl EffectCommandGenerator {
    /// 根据效果参数生成FFmpeg过滤器命令
    /// 
    /// # 参数
    /// * `effect` - 效果参数对象，包含效果类型和具体参数
    /// 
    /// # 返回
    /// * `Ok(String)` - 成功时返回过滤器命令字符串
    /// * `Err(String)` - 失败时返回技术错误信息
    ///
    /// # 示例
    /// ```
    /// let effect = EffectParam {
    ///     effect_id: "effect-001".to_string(),
    ///     effect_type: "speed".to_string(),
    ///     params: json!({"speed": 2.0})
    /// };
    /// let filter = EffectCommandGenerator::generate_filter(&effect)?;
    /// ```
    pub fn generate_filter(effect: &EffectParam) -> Result<String, String> {
        // 根据效果类型选择对应的处理函数
        // 添加新效果时，只需在此添加新的匹配分支
        match effect.effect_type.as_str() {
            "speed" => Self::generate_speed_filter(effect),
            "crop" => Self::generate_crop_filter(effect),
            "watermark" => Self::generate_watermark_filter(effect),
            "color" => Self::generate_color_filter(effect),
            // 新效果在此添加...
            _ => Err(format!("unsupported_effect_type: {}", effect.effect_type))
        }
    }
    
    /// 生成速度效果的FFmpeg过滤器
    /// 
    /// 参数:
    /// - speed: 速度因子，大于1加速，小于1减速
    /// - keep_audio_pitch: 是否保持音频音调
    fn generate_speed_filter(effect: &EffectParam) -> Result<String, String> {
        // 获取速度参数
        let speed = match effect.params.get("speed").and_then(|v| v.as_f64()) {
            Some(s) => s,
            None => return Err("speed_param_invalid".to_string())
        };
            
        // 获取是否保持音频音调参数
        let keep_audio_pitch = effect.params.get("keep_audio_pitch")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);
        
        // 视频速度过滤器
        let video_filter = format!("setpts=PTS/{}", speed);
        
        // 音频速度过滤器
        let audio_filter = if keep_audio_pitch {
            // 保持音调时使用asetrate和aresample
            format!("asetrate=44100*{},aresample=44100", speed)
        } else {
            // 不保持音调时直接使用atempo
            format!("atempo={}", speed)
        };
        
        // 组合过滤器
        Ok(format!("{},{}", video_filter, audio_filter))
    }
    
    /// 生成裁剪效果的FFmpeg过滤器
    /// 
    /// 参数:
    /// - x: 裁剪起始X坐标
    /// - y: 裁剪起始Y坐标
    /// - width: 裁剪宽度
    /// - height: 裁剪高度
    fn generate_crop_filter(effect: &EffectParam) -> Result<String, String> {
        // 辅助函数: 提取u32类型参数
        let get_u32 = |name: &str| -> Result<u32, String> {
            match effect.params.get(name).and_then(|v| v.as_u64()) {
                Some(val) => Ok(val as u32),
                None => Err(format!("{}_param_invalid", name))
            }
        };

        // 获取所有裁剪参数
        let x = get_u32("x")?;
        let y = get_u32("y")?;
        let width = get_u32("width")?;
        let height = get_u32("height")?;

        // 生成裁剪过滤器
        Ok(format!("crop={}:{}:{}:{}", width, height, x, y))
    }
    
    /// 生成水印效果的FFmpeg过滤器
    /// 
    /// 参数:
    /// - text: 水印文本内容
    /// - position_x: 水印X坐标
    /// - position_y: 水印Y坐标
    /// - font_size: 字体大小
    /// - color: 水印颜色 (可选，默认白色)
    fn generate_watermark_filter(effect: &EffectParam) -> Result<String, String> {
        // 获取水印文本
        let text = match effect.params.get("text").and_then(|v| v.as_str()) {
            Some(t) => t,
            None => return Err("text_param_invalid".to_string())
        };

        // 获取位置参数
        let position_x = match effect.params.get("position_x").and_then(|v| v.as_u64()) {
            Some(x) => x as u32,
            None => return Err("position_x_param_invalid".to_string())
        };

        let position_y = match effect.params.get("position_y").and_then(|v| v.as_u64()) {
            Some(y) => y as u32,
            None => return Err("position_y_param_invalid".to_string())
        };

        // 获取字体大小
        let font_size = match effect.params.get("font_size").and_then(|v| v.as_u64()) {
            Some(size) => size as u32,
            None => return Err("font_size_param_invalid".to_string())
        };
        
        // 获取颜色参数（可选）
        let color = effect.params.get("color")
            .and_then(|v| v.as_str())
            .unwrap_or("white");
        
        // 生成水印过滤器
        Ok(format!(
            "drawtext=text='{}':x={}:y={}:fontsize={}:fontcolor={}",
            text, position_x, position_y, font_size, color
        ))
    }
    
    /// 生成颜色调整效果的FFmpeg过滤器
    /// 
    /// 参数:
    /// - brightness: 亮度调整 (-1.0 to 1.0)
    /// - contrast: 对比度调整 (0.0 to 2.0)
    /// - saturation: 饱和度调整 (0.0 to 3.0)
    fn generate_color_filter(effect: &EffectParam) -> Result<String, String> {
        // 辅助函数: 提取f32类型参数，带默认值
        let get_f32 = |name: &str, default: f32| -> f32 {
            effect.params.get(name)
                .and_then(|v| v.as_f64())
                .map(|v| v as f32)
                .unwrap_or(default)
        };

        // 获取颜色参数
        let brightness = get_f32("brightness", 0.0);
        let contrast = get_f32("contrast", 1.0);
        let saturation = get_f32("saturation", 1.0);

        // 生成颜色调整过滤器
        Ok(format!(
            "eq=brightness={}:contrast={}:saturation={}",
            brightness, contrast, saturation
        ))
    }
}

/// 预览请求
#[derive(Debug, Serialize, Deserialize)]
pub struct PreviewRequest {
    /// 输入视频路径
    pub input_path: String,
    
    /// 效果参数
    pub effect: EffectParam,
} 