{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2241668132362809309, "path": 11589258767221456915, "deps": [[1573238666360410412, "rand_chacha", false, 9408800588426779083], [18130209639506977569, "rand_core", false, 17073666097122250592]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-581a7afe36a0ee5f\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}