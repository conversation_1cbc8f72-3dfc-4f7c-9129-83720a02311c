# E剪Pro Tauri重构架构方案

## 🎯 重构目标
- 统一技术栈：全Rust生态
- 减少代码量：预计减少60%+的前端代码
- 提升性能：更小包体积，更低内存占用
- 改善开发体验：现代前端框架 + Rust后端

## 🏗️ 新架构设计

### 1. 项目结构
```
ECutPro-Tauri/
├── src-tauri/           # Tauri Rust后端
│   ├── src/
│   │   ├── main.rs      # Tauri应用入口
│   │   ├── commands.rs  # Tauri命令定义
│   │   ├── state.rs     # 应用状态管理
│   │   └── video/       # 视频处理模块（复用现有代码）
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                 # 前端代码（Vue3 + TypeScript）
│   ├── components/      # Vue组件
│   ├── stores/          # Pinia状态管理
│   ├── types/           # TypeScript类型定义
│   └── utils/           # 工具函数
├── package.json
└── vite.config.ts
```

### 2. 核心模块设计

#### Tauri Commands（替代TCP通信）
```rust
// src-tauri/src/commands.rs
#[tauri::command]
async fn process_videos(
    input_paths: Vec<String>,
    output_dir: String,
    effects: Vec<EffectParam>,
    options: ProcessingOptions,
) -> Result<String, String> {
    // 直接调用现有的视频处理服务
    video_service.process_videos(ProcessRequest {
        input_paths,
        output_dir,
        effects,
        options,
    }).await
}

#[tauri::command]
async fn analyze_video(path: String) -> Result<VideoMetadata, String> {
    // 复用现有分析服务
    analysis_service.analyze_video(&path).await
}
```

#### 前端状态管理（Pinia）
```typescript
// src/stores/videoStore.ts
export const useVideoStore = defineStore('video', () => {
  const files = ref<VideoFile[]>([])
  const processing = ref(false)
  const progress = ref(0)
  
  const addFiles = async (paths: string[]) => {
    // 调用Tauri命令
    await invoke('add_video_files', { paths })
  }
  
  const processVideos = async () => {
    processing.value = true
    try {
      await invoke('process_videos', {
        inputPaths: files.value.map(f => f.path),
        outputDir: settings.outputPath,
        effects: enabledEffects.value,
        options: processingOptions.value
      })
    } finally {
      processing.value = false
    }
  }
  
  return { files, processing, progress, addFiles, processVideos }
})
```

### 3. 事件系统（替代TCP推送）
```rust
// Tauri事件发射
app.emit_all("video_progress", ProgressPayload {
    task_id: task_id.clone(),
    progress: progress_percent,
    global_progress: calculate_global_progress(),
}).unwrap();
```

```typescript
// 前端事件监听
import { listen } from '@tauri-apps/api/event'

listen<ProgressPayload>('video_progress', (event) => {
  updateProgress(event.payload)
})
```

## 🔧 代码复用策略

### 1. 直接复用的模块
- `ffmpeg/` - FFmpeg集成层
- `models/` - 数据模型
- `utils/` - 工具函数
- `services/video_processing.rs` - 核心处理逻辑

### 2. 需要适配的模块
- `ipc/` → Tauri Commands
- TCP通信 → Tauri事件系统
- 前端UI → Vue3组件

### 3. 可以删除的代码
- 整个TCP服务器实现
- C# Avalonia UI代码
- .NET相关配置

## 📊 预期收益

### 代码量对比
- **当前**: C# ~3000行 + Rust ~2000行 = 5000行
- **重构后**: Vue3 ~1200行 + Rust ~2500行 = 3700行
- **减少**: ~25% 总代码量

### 性能提升
- **包体积**: 从~50MB降至~15MB
- **内存占用**: 从~150MB降至~50MB
- **启动速度**: 提升50%+

### 开发体验
- 统一的Rust工具链
- 现代前端开发体验
- 更好的类型安全
- 简化的构建流程

## 🚀 实施计划

### Phase 1: 基础架构搭建
1. 创建Tauri项目
2. 设置Vue3 + TypeScript
3. 配置基础的Tauri命令

### Phase 2: 核心功能迁移
1. 迁移视频处理逻辑
2. 实现文件管理功能
3. 重建效果系统

### Phase 3: UI重构
1. 设计现代化Vue组件
2. 实现响应式布局
3. 添加动画和交互效果

### Phase 4: 优化和测试
1. 性能优化
2. 错误处理完善
3. 全面测试

## 🎨 UI框架选择建议

推荐使用 **Vue3 + Naive UI**：
- Vue3的组合式API非常适合复杂状态管理
- Naive UI提供完整的组件库
- TypeScript支持优秀
- 与Tauri集成度高
