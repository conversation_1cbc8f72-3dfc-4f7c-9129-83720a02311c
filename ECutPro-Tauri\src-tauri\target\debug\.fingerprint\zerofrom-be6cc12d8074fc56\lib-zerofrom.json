{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2225463790103693989, "path": 6870722025311395505, "deps": [[4022439902832367970, "zerofrom_derive", false, 15073951816299852871]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-be6cc12d8074fc56\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}