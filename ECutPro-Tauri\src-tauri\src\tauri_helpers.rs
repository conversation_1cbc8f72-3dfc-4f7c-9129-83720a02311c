// ******************************************************
// Tauri辅助函数 - 保持你的完美设计风格
//
// 设计理念：
//   1. 保持原有的success/error函数设计 ✅
//   2. 统一的消息格式 - UniversalMessage ✅
//   3. 全局辅助函数 - 所有服务都使用 ✅
//   4. 简洁优雅 - 一行代码发送消息 ✅
//   5. 1:1替换 - 保持原有调用方式 ✅
// ******************************************************

use serde::{Deserialize, Serialize};
use serde_json::Value;
use tauri::{AppHandle, Emitter};
use log::error;

/// 通用消息结构 - 保持与原项目完全一致
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UniversalMessage {
    /// 是否成功
    pub success: bool,
    /// 数据载荷（JSON格式，可以包含任何数据）
    pub data: Value,
    /// 用户友好的消息（成功时可选，失败时必需）
    pub message: Option<String>,
}

/// 创建成功消息 - 保持原有设计
///
/// 所有服务的成功响应都使用这个函数
///
/// # 参数
/// * `data` - 任意JSON数据
///
/// # 示例
/// ```
/// // 视频处理进度
/// success(json!({"task_id": "video1.mp4", "progress": "处理中 (45%)"}))
///
/// // 预览结果
/// success(json!({"frames_directory": "./temp/frames/"}))
///
/// // 分析结果
/// success(json!({"video_path": "video.mp4", "metadata": metadata}))
/// ```
pub fn success(data: Value) -> UniversalMessage {
    UniversalMessage {
        success: true,
        data,
        message: None,
    }
}

/// 创建失败消息 - 保持原有设计
///
/// 所有服务的失败响应都使用这个函数
///
/// # 参数
/// * `message` - 用户友好的错误信息
///
/// # 示例
/// ```
/// // 视频处理失败
/// error("视频处理失败，请检查文件格式".to_string())
///
/// // 预览失败
/// error("预览生成失败，请稍后重试".to_string())
///
/// // 分析失败
/// error("无法分析视频文件，请确认文件格式是否受支持".to_string())
/// ```
pub fn error(message: String) -> UniversalMessage {
    UniversalMessage {
        success: false,
        data: Value::Null,
        message: Some(message),
    }
}

/// Tauri事件发送器 - 1:1替换TCP发送器
///
/// 保持与原有TcpDuplexServer完全一致的调用体验
/// 这样你的所有业务逻辑代码都不需要改变！
#[derive(Clone)]
pub struct TauriSender {
    app_handle: AppHandle,
}

impl TauriSender {
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle }
    }

    /// 发送视频处理消息 - 完全保持原有接口
    ///
    /// 1:1替换：tcp_server.send_video_processing() → tauri_sender.send_video_processing()
    pub async fn send_video_processing(&self, message: UniversalMessage) {
        if let Err(e) = self.app_handle.emit_all("video_processing", &message) {
            error!("❌ 发送视频处理消息失败: {}", e);
        }
    }

    /// 发送视频分析消息 - 完全保持原有接口
    pub async fn send_video_analysis(&self, message: UniversalMessage) {
        if let Err(e) = self.app_handle.emit_all("video_analysis", &message) {
            error!("❌ 发送视频分析消息失败: {}", e);
        }
    }

    /// 发送预览消息 - 完全保持原有接口
    pub async fn send_preview(&self, message: UniversalMessage) {
        if let Err(e) = self.app_handle.emit_all("preview", &message) {
            error!("❌ 发送预览消息失败: {}", e);
        }
    }

    /// 发送GPU检查消息 - 完全保持原有接口
    pub async fn send_gpu_check(&self, message: UniversalMessage) {
        if let Err(e) = self.app_handle.emit_all("gpu_check", &message) {
            error!("❌ 发送GPU检查消息失败: {}", e);
        }
    }
}
