use serde::{Deserialize, Serialize};

/// 处理选项 - 前端UI选项配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct ProcessingOptions {
    /// 输出分辨率选项索引
    #[serde(default, alias = "outputResolutionIndex")]
    pub output_resolution_index: i32,

    /// 输出质量选项索引
    #[serde(default, alias = "outputQualityIndex")]
    pub output_quality_index: i32,

    /// 是否删除原文件选项索引
    #[serde(default, alias = "deleteOriginalIndex")]
    pub delete_original_index: i32,

    /// 命名规则选项索引
    #[serde(default, alias = "namingRuleIndex")]
    pub naming_rule_index: i32,

    /// 处理线程数选项索引
    #[serde(default, alias = "threadCountIndex")]
    pub thread_count_index: i32,

    /// GPU加速选项索引
    #[serde(default, alias = "gpuAccelerationIndex")]
    pub gpu_acceleration_index: i32,

    /// 输出格式选项索引
    /// 0: 原格式, 1: MP4, 2: MOV, 3: MKV
    #[serde(default, alias = "outputFormatIndex")]
    pub output_format_index: i32,

    /// 忽略前端发送的额外字段
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub id: Option<i32>,

    /// 输出路径（从前端的outputPath字段映射）
    #[serde(default, alias = "outputPath")]
    pub output_path: Option<String>,
}

/// 处理请求 - 前端发送的完整处理请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessRequest {
    /// 输入视频路径列表
    pub input_paths: Vec<String>,

    /// 输出目录（从options.output_path获取）
    #[serde(skip_deserializing, default)]
    pub output_dir: String,

    /// 效果参数列表
    pub effects: Vec<crate::models::effect_params::EffectParam>,

    /// 处理选项
    #[serde(default)]
    pub options: ProcessingOptions,
}

impl ProcessRequest {
    /// 处理前端数据，提取output_dir（纯技术实现，不记录日志）
    ///
    /// # 返回
    /// * `Ok(())` - 成功提取输出目录
    /// * `Err(String)` - 前端未提供输出路径时返回技术错误信息
    pub fn normalize(&mut self) -> Result<(), String> {
        if let Some(output_path) = &self.options.output_path {
            self.output_dir = output_path.clone();
            Ok(())
        } else {
            // 返回技术错误信息，让服务层处理日志和用户友好消息
            Err("output_path_not_provided".to_string())
        }
    }
}
