// ******************************************************
// 文件名: VideoModels.cs
// 功能描述: 视频相关模型
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 定义视频文件信息类
//   2. 定义处理选项类
// ******************************************************

using System;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;
using LiteDB;

namespace ECutPro.Models
{
    /// <summary>
    /// 视频元数据类
    /// </summary>
    public partial class VideoMetadata : ObservableObject
    {
        /// <summary>
        /// 视频分辨率
        /// </summary>
        [ObservableProperty]
        private string _resolution = "未知";
        
        /// <summary>
        /// 视频码率
        /// </summary>
        [ObservableProperty]
        private string _bitrate = "未知";
        
        /// <summary>
        /// 视频帧率
        /// </summary>
        [ObservableProperty]
        private string _frameRate = "未知";
        
        /// <summary>
        /// 是否有字幕
        /// </summary>
        [ObservableProperty]
        private string _hasSubtitles = "未知";
        
        /// <summary>
        /// 视频时长
        /// </summary>
        [ObservableProperty]
        private string _duration = "未知";
    }
    
    /// <summary>
    /// 视频文件信息类
    /// </summary>
    public partial class VideoFileInfo : ObservableObject, IDisposable
    {
        /// <summary>
        /// 视频ID（用于UI显示）
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }
        
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }
        
        /// <summary>
        /// 格式化后的文件大小
        /// </summary>
        public string FileSizeFormatted { get; set; }
        
        /// <summary>
        /// 视频处理状态
        /// </summary>
        [ObservableProperty]
        private string _status = "待处理";
        
        /// <summary>
        /// 创建一个新的视频文件信息实例
        /// </summary>
        public VideoFileInfo(string filePath)
        {
            FilePath = filePath;
            FileName = Path.GetFileName(filePath);
            
            // 获取文件大小并直接格式化
            long length = new FileInfo(filePath).Length;
            
            // 使用简单的条件逻辑直接格式化，保留小数点后两位
            if (length < 1024)
                FileSizeFormatted = $"{length} B";
            else if (length < 1024 * 1024)
                FileSizeFormatted = $"{length / 1024.0:0.00} KB";
            else if (length < 1024 * 1024 * 1024)
                FileSizeFormatted = $"{length / (1024.0 * 1024):0.00} MB";
            else
                FileSizeFormatted = $"{length / (1024.0 * 1024 * 1024):0.00} GB";
        }

        public void Dispose()
        {
            // 当前VideoFileInfo没有需要释放的资源
            // 此方法为将来可能添加的资源预留
            //触发垃圾回收
            // GC.Collect();
            // GC.WaitForPendingFinalizers();
        }
    }
    
    /// <summary>
    /// 处理选项类
    /// </summary>
    public partial class ProcessingOptions : ObservableObject
    {
        /// <summary>
        /// 数据库ID
        /// </summary>
        [BsonId]
        public int Id { get; set; }
        
        /// <summary>
        /// 输出分辨率选项
        /// </summary>
        [ObservableProperty]
        private int _outputResolutionIndex = 0;
        
        /// <summary>
        /// 输出质量选项
        /// 0: 原质量, 1: 无损, 2: 高质量, 3: 标准质量, 4: 低质量
        /// </summary>
        [ObservableProperty]
        private int _outputQualityIndex = 0;
        
        /// <summary>
        /// 是否删除原文件选项
        /// </summary>
        [ObservableProperty]
        private int _deleteOriginalIndex = 0;
        
        /// <summary>
        /// 命名规则选项
        /// </summary>
        [ObservableProperty]
        private int _namingRuleIndex = 0;
        
        /// <summary>
        /// 处理线程数选项
        /// </summary>
        [ObservableProperty]
        private int _threadCountIndex = 0;
        
        /// <summary>
        /// GPU加速选项
        /// </summary>
        [ObservableProperty]
        private int _gpuAccelerationIndex = 0;
        
        /// <summary>
        /// 输出格式选项
        /// 0: 原格式, 1: MP4, 2: MOV, 3: MKV
        /// </summary>
        [ObservableProperty]
        private int _outputFormatIndex = 0;
        
        /// <summary>
        /// 输出路径
        /// </summary>
        [ObservableProperty]
        private string _outputPath = string.Empty;
    }
} 