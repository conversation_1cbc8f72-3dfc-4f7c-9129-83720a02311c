<!DOCTYPE html>
<html>
<!-- Created by GNU Texinfo 7.0.1, https://www.gnu.org/software/texinfo/ -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Libavformat Documentation</title>

<meta name="description" content="Libavformat Documentation">
<meta name="keywords" content="Libavformat Documentation">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta name="viewport" content="width=device-width,initial-scale=1">

<link href="#SEC_Contents" rel="contents" title="Table of Contents">
<style type="text/css">
<!--
ul.toc-numbered-mark {list-style: none}
-->
</style>


</head>

<body lang="en">


<div class="top-level-extent" id="SEC_Top">


<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-See-Also" href="#See-Also">2 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">3 Authors</a></li>
</ul>
</div>
</div>
<div class="chapter-level-extent" id="Description">
<h2 class="chapter">1 Description</h2>

<p>The libavformat library provides a generic framework for multiplexing
and demultiplexing (muxing and demuxing) audio, video and subtitle
streams. It encompasses multiple muxers and demuxers for multimedia
container formats.
</p>
<p>It also supports several input and output protocols to access a media
resource.
</p>

</div>
<div class="chapter-level-extent" id="See-Also">
<h2 class="chapter">2 See Also</h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="ffmpeg-formats.html">ffmpeg-formats</a>, <a class="url" href="ffmpeg-protocols.html">ffmpeg-protocols</a>,
<a class="url" href="libavutil.html">libavutil</a>, <a class="url" href="libavcodec.html">libavcodec</a>
</p>

</div>
<div class="chapter-level-extent" id="Authors">
<h2 class="chapter">3 Authors</h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(git://source.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="http://source.ffmpeg.org">http://source.ffmpeg.org</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

</div>
</div>



</body>
</html>
