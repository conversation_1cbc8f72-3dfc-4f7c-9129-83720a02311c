<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:ECutPro.ViewModels"
        xmlns:conv="using:ECutPro.Converters"
        xmlns:videoEffects="using:ECutPro.VideoEffects"
        xmlns:controls="using:ECutPro.Controls"
        mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="700"
        x:Class="ECutPro.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Title="E剪 Pro"
        MinWidth="900" MinHeight="600"
        Width="1200" Height="800"
        WindowStartupLocation="CenterScreen"
        Background="#FF212121"
        Foreground="White"
        ExtendClientAreaToDecorationsHint="True"
        ExtendClientAreaChromeHints="NoChrome"
        SystemDecorations="None"
        ShowInTaskbar="True"
        CanResize="False"
        FontSize="12">

    <Window.Resources>
        <RadialGradientBrush x:Key="VignetteGradient" Center="0.5,0.5" RadiusX="1" RadiusY="1" GradientOrigin="0.5,0.5">
            <GradientStop Offset="0" Color="#00000000"/>
            <GradientStop Offset="0.8" Color="#00000000"/>
            <GradientStop Offset="1" Color="#30000000"/>
        </RadialGradientBrush>
    </Window.Resources>

    <Design.DataContext>
        <vm:MainWindowViewModel />
    </Design.DataContext>

    <Window.Styles>
        <!-- 窗口控制按钮样式 -->
        <Style Selector="Button.WindowControlButton">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Width" Value="46"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        
        <!-- 窗口控制按钮图标样式 -->
        <Style Selector="Button.WindowControlButton Path">
            <Setter Property="Stroke" Value="#EEEEEE"/>
            <Setter Property="StrokeThickness" Value="1.5"/>
        </Style>
        
        <!-- 窗口控制按钮图标悬停样式 -->
        <Style Selector="Button.WindowControlButton:pointerover Path">
            <Setter Property="Stroke" Value="White"/>
        </Style>
        
        <!-- 加载动画样式 -->
        <Style Selector="Ellipse.LoadingCircle">
            <Style.Animations>
                <Animation Duration="0:0:1" IterationCount="Infinite">
                    <KeyFrame Cue="0%">
                        <Setter Property="RotateTransform.Angle" Value="0"/>
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="RotateTransform.Angle" Value="360"/>
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </Window.Styles>

    <Grid Background="#FF212121" Name="MainGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Grid Grid.Row="0" Background="#FF212121">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 标题和Logo -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="10" Margin="15,0,0,0">
                <Canvas Width="20" Height="20" VerticalAlignment="Center">
                    <Ellipse Width="18" Height="18" Fill="#30C9B0" Canvas.Left="1" Canvas.Top="1"/>
                    <Path Data="M7,7 L13,7 L13,13 L7,13 Z" Fill="#FF212121" Canvas.Left="1" Canvas.Top="1"/>
                </Canvas>
                <TextBlock Text="E剪 Pro" Foreground="White" 
                           FontWeight="SemiBold" FontSize="14" 
                           VerticalAlignment="Center"/>
            </StackPanel>
            
            <!-- 拖拽区域 -->
            <Grid Grid.Column="1" Background="Transparent" PointerPressed="DragArea_PointerPressed"/>
            
            <!-- 窗口控制按钮 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top">
                <!-- 最小化按钮 -->
                <Button Click="MinimizeButton_Click" Classes="WindowControlButton">
                    <Canvas Width="12" Height="12">
                        <Path Data="M1,6 L11,6" />
                    </Canvas>
                </Button>
                
                <!-- 最大化按钮（已禁用） -->
                <Button Click="MaximizeButton_Click" Classes="WindowControlButton" IsVisible="False">
                    <Canvas Width="12" Height="12">
                        <Path Data="M1,1 L11,1 L11,11 L1,11 Z" />
                    </Canvas>
                </Button>
                
                <!-- 关闭按钮 -->
                <Button Click="CloseButton_Click" Classes="WindowControlButton">
                    <Canvas Width="12" Height="12">
                        <Path Data="M1,1 L11,11 M1,11 L11,1" />
                    </Canvas>
                </Button>
            </StackPanel>
        </Grid>
        
        <!-- 主内容 -->
        <Grid Grid.Row="1" Margin="12,12,12,14">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 主要内容区域 -->
            <Grid Grid.Row="0" Margin="0,0,0,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="3*" MinWidth="500"/>
                    <ColumnDefinition Width="2*" MinWidth="300"/>
                </Grid.ColumnDefinitions>
                
                <!-- 左侧文件区域 -->
                <Border Classes="UniformBorder" Grid.Column="0" Margin="0,0,6,0" Padding="15" 
                       Background="#222222" BorderBrush="Transparent" BorderThickness="0"
                       CornerRadius="16" BoxShadow="0 5 30 5 #60000000">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 文件操作按钮 -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 左侧按钮组 -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8">
                                <Button Content="添加视频" Classes="StandardButton" Command="{Binding AddVideoFilesCommand}" 
                                       HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                <Button Content="添加文件夹" Classes="StandardButton" Command="{Binding AddFolderCommand}" 
                                       HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                            </StackPanel>
                            
                            <!-- 右侧输出路径区域 -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="5" VerticalAlignment="Center">
                                <TextBlock Text="输出路径:" FontSize="11" Foreground="#BBBBBB" VerticalAlignment="Center"/>
                                <Border Background="#282828" CornerRadius="4" Padding="8,4" Height="26" Width="240" VerticalAlignment="Center">
                                    <Panel>
                                        <TextBlock Text="使用视频所在的原目录作为输出目录" 
                                                 FontSize="11" Foreground="#30C9B0" VerticalAlignment="Center" 
                                                 IsVisible="{Binding ProcessingOptions.OutputPath, Converter={x:Static StringConverters.IsNullOrEmpty}}">
                                            <ToolTip.Tip>
                                                <TextBlock Text="使用视频所在的原目录作为输出目录"/>
                                            </ToolTip.Tip>
                                        </TextBlock>
                                        <TextBlock Text="{Binding ProcessingOptions.OutputPath}" 
                                                 FontSize="11" Foreground="#30C9B0" VerticalAlignment="Center" 
                                                 TextTrimming="CharacterEllipsis"
                                                 IsVisible="{Binding ProcessingOptions.OutputPath, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                                            <ToolTip.Tip>
                                                <TextBlock Text="{Binding ProcessingOptions.OutputPath}"/>
                                            </ToolTip.Tip>
                                        </TextBlock>
                                    </Panel>
                                </Border>
                                <Button Content="选择" Classes="StandardButton" Command="{Binding SelectOutputPathCommand}" 
                                        Width="50" Height="26" Padding="0" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                        
                        <!-- 文件列表区域 -->
                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <!-- 列表标题行 -->
                            <Border Grid.Row="0" Background="#2C2C2C" Height="36" CornerRadius="6,6,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" MinWidth="40"/>
                                        <ColumnDefinition Width="40*"/>
                                        <ColumnDefinition Width="30*"/>
                                        <ColumnDefinition Width="15*"/>
                                        <ColumnDefinition Width="15*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="ID" FontWeight="SemiBold" FontSize="11" Margin="15,0,15,0" VerticalAlignment="Center" Foreground="#BBBBBB"/>
                                    <TextBlock Grid.Column="1" Text="文件名" FontWeight="SemiBold" FontSize="11" Margin="5,0,0,0" VerticalAlignment="Center" Foreground="#BBBBBB"/>
                                    <TextBlock Grid.Column="2" Text="文件路径" FontWeight="SemiBold" FontSize="11" Margin="10,0,0,0" VerticalAlignment="Center" Foreground="#BBBBBB"/>
                                    <TextBlock Grid.Column="3" Text="文件大小" FontWeight="SemiBold" FontSize="11" Margin="0" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="#BBBBBB"/>
                                    <TextBlock Grid.Column="4" Text="处理状态" FontWeight="SemiBold" FontSize="11" Margin="0" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="#BBBBBB"/>
                                </Grid>
                            </Border>
                            
                            <!-- 文件列表项 -->
                            <Border Grid.Row="1" Background="#262626" CornerRadius="0,0,6,6">
                                <Panel>
                                    <ListBox ItemsSource="{Binding VideoFiles}"
                                             x:Name="VideoListBox"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             Padding="0"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                             IsHitTestVisible="{Binding VideoFilesLoaded}"
                                             SelectionMode="Multiple"
                                             SelectedIndex="{Binding SelectedIndex}">
                                        <ListBox.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Header="移除选中视频" 
                                                          Click="RemoveSelectedVideos_Click"/>
                                                <Separator/>
                                                <MenuItem Header="清空所有视频" 
                                                          Click="ClearAllVideos_Click"/>
                                            </ContextMenu>
                                        </ListBox.ContextMenu>
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Grid Height="36" Background="Transparent">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto" MinWidth="40"/>
                                                        <ColumnDefinition Width="40*"/>
                                                        <ColumnDefinition Width="30*"/>
                                                        <ColumnDefinition Width="15*"/>
                                                        <ColumnDefinition Width="15*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Grid.Column="0" Text="{Binding Id, FallbackValue=1}" Margin="15,0,15,0" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB">
                                                        <ToolTip.Tip>
                                                            <TextBlock Text="{Binding Id, FallbackValue=1}"/>
                                                        </ToolTip.Tip>
                                                    </TextBlock>
                                                    <TextBlock Grid.Column="1" Text="{Binding FileName}" Margin="5,0,0,0" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"
                                                               TextTrimming="CharacterEllipsis">
                                                        <ToolTip.Tip>
                                                            <TextBlock Text="{Binding FileName}"/>
                                                        </ToolTip.Tip>
                                                    </TextBlock>
                                                    <TextBlock Grid.Column="2" Text="{Binding FilePath}" Margin="10,0" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"
                                                               TextTrimming="CharacterEllipsis">
                                                        <ToolTip.Tip>
                                                            <TextBlock Text="{Binding FilePath}"/>
                                                        </ToolTip.Tip>
                                                    </TextBlock>
                                                    <TextBlock Grid.Column="3" Text="{Binding FileSizeFormatted}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB">
                                                        <ToolTip.Tip>
                                                            <TextBlock Text="{Binding FileSizeFormatted}"/>
                                                        </ToolTip.Tip>
                                                    </TextBlock>
                                                    <TextBlock Grid.Column="4" Text="{Binding Status}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="11" Foreground="#30C9B0">
                                                        <ToolTip.Tip>
                                                            <TextBlock Text="{Binding Status}"/>
                                                        </ToolTip.Tip>
                                                    </TextBlock>
                                                </Grid>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>
                                        <ListBox.Styles>
                                            <Style Selector="ListBoxItem">
                                                <Setter Property="Padding" Value="0"/>
                                                <Setter Property="Margin" Value="0"/>
                                                <Setter Property="Background" Value="Transparent"/>
                                                <Setter Property="BorderThickness" Value="0"/>
                                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                            </Style>
                                            <Style Selector="ListBoxItem:pointerover /template/ ContentPresenter">
                                                <Setter Property="Background" Value="#30333333"/>
                                            </Style>
                                            <Style Selector="ListBoxItem:selected /template/ ContentPresenter">
                                                <Setter Property="Background" Value="#363636"/>
                                            </Style>
                                        </ListBox.Styles>
                                    </ListBox>
                                    
                                    <!-- 空列表提示信息 -->
                                    <Grid IsVisible="{Binding !VideoFilesLoaded}" Opacity="1" 
                                         IsHitTestVisible="False">
                                        <Grid.Transitions>
                                            <Transitions>
                                                <DoubleTransition Property="Opacity" Duration="0:0:0.2"/>
                                            </Transitions>
                                        </Grid.Transitions>
                                        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Spacing="15" 
                                                  IsHitTestVisible="False">
                                            <Border Width="70" Height="70" 
                                                   Background="#15FFFFFF" 
                                                   CornerRadius="35"
                                                   IsHitTestVisible="False">
                                                <TextBlock Text="&#xE8E5;" 
                                                         FontFamily="Segoe MDL2 Assets" 
                                                         FontSize="35" 
                                                         Foreground="#777777"
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center" 
                                                         IsHitTestVisible="False"/>
                                            </Border>
                                            <StackPanel Spacing="10" IsHitTestVisible="False">
                                                <TextBlock Text="请点击上方添加视频按钮" 
                                                         Foreground="#BBBBBB" 
                                                         FontSize="14"
                                                         FontWeight="SemiBold"
                                                         HorizontalAlignment="Center"
                                                         IsHitTestVisible="False"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </Panel>
                            </Border>
                        </Grid>
                        
                        <!-- 文件区域底部视频信息显示 -->
                        <Border Grid.Row="2" Background="#262626" CornerRadius="6" Margin="0,10,0,0">
                            <StackPanel Orientation="Horizontal" Spacing="16" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="15,10">
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <TextBlock Text="分辨率:" FontSize="11" Foreground="#BBBBBB"/>
                                    <TextBlock Text="{Binding VideoMetadata.Resolution}" FontSize="11" Foreground="#BBBBBB" FontWeight="SemiBold"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <TextBlock Text="码率:" FontSize="11" Foreground="#BBBBBB"/>
                                    <TextBlock Text="{Binding VideoMetadata.Bitrate}" FontSize="11" Foreground="#BBBBBB" FontWeight="SemiBold"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <TextBlock Text="帧率:" FontSize="11" Foreground="#BBBBBB"/>
                                    <TextBlock Text="{Binding VideoMetadata.FrameRate}" FontSize="11" Foreground="#BBBBBB" FontWeight="SemiBold"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <TextBlock Text="字幕:" FontSize="11" Foreground="#BBBBBB"/>
                                    <TextBlock Text="{Binding VideoMetadata.HasSubtitles}" FontSize="11" Foreground="#BBBBBB" FontWeight="SemiBold"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal" Spacing="5">
                                    <TextBlock Text="时长:" FontSize="11" Foreground="#BBBBBB"/>
                                    <TextBlock Text="{Binding VideoMetadata.Duration}" FontSize="11" Foreground="#BBBBBB" FontWeight="SemiBold"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>
                
                <!-- 右侧功能区域 -->
                <Border Classes="UniformBorder" Grid.Column="1" Margin="6,0,0,0" Padding="15"
                       Background="#222222" BorderBrush="Transparent" BorderThickness="0"
                       CornerRadius="16" BoxShadow="0 5 30 5 #60000000">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 功能标题 -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="视频功能设置" FontSize="18" FontWeight="SemiBold" Foreground="#BBBBBB" VerticalAlignment="Center"/>
                            <Border Grid.Column="1" Background="#282828" CornerRadius="4" Padding="10,5">
                                <TextBlock Text="点击下方卡片设置相应功能" FontSize="11" Foreground="#BBBBBB"/>
                            </Border>
                        </Grid>
                        
                        <!-- 视频效果功能卡片区域 -->
                        <Border Grid.Row="1" Classes="CardBorder" Margin="0" Padding="10,10,10,10" Background="Transparent" BoxShadow="0 0 0 0 #00000000">
                            <StackPanel HorizontalAlignment="Stretch">
                                <ItemsControl ItemsSource="{Binding VideoEffects}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <UniformGrid Columns="2" Rows="2" Margin="0,5,0,0"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate DataType="{x:Type videoEffects:VideoEffect}">
                                            <Border Margin="5,0,5,16" Classes="EffectCard"
                                                    Tag="{Binding Tag}"
                                                    Classes.EffectCardActive="{Binding IsEnabled}"
                                                    Tapped="OnEffectCardTapped">
                                                <Grid ColumnDefinitions="Auto,*,Auto">
                                                    <!-- 效果图标 -->
                                                    <Border Grid.Column="0" Classes="IconContainer" Margin="-6,0,8,0"
                                                            Classes.IconContainerActive="{Binding IsEnabled}">
                                                        <Path Data="{Binding IconPath}" 
                                                              Fill="#30C9B0" Stroke="#30C9B0" StrokeThickness="1.5" 
                                                              Width="16" Height="16" 
                                                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    
                                                    <!-- 效果标题和描述 -->
                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Spacing="2">
                                                        <TextBlock Text="{Binding Title}" Classes="EffectTitle"
                                                                   Classes.EffectTitleActive="{Binding IsEnabled}"/>
                                                        <TextBlock Text="{Binding Description}" Classes="EffectDescription"
                                                                   Classes.EffectDescriptionActive="{Binding IsEnabled}"/>
                                                    </StackPanel>
                                                    
                                                    <!-- 箭头图标 -->
                                                    <Border Grid.Column="2" Classes="ArrowContainer"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>
            </Grid>
            
            <!-- 底部状态栏和控制区域 -->
            <Grid Grid.Row="1" Margin="0,2,0,0">
                <!-- 外部圆角边框 - macOS Dock风格 -->
                <Border Background="#282828" BorderBrush="Transparent" BorderThickness="0" CornerRadius="16" 
                        BoxShadow="0 5 30 5 #60000000" Margin="10,0" Classes="UniformBorder">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 控制和统计信息区域 -->
                        <Grid Grid.Row="0" Height="46">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 控制区域 (居中显示：输出分辨率、输出质量、删除原文件、命名规则、处理线程、GPU加速、处理按钮) -->
                            <Grid Grid.Column="1" HorizontalAlignment="Center">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- 输出分辨率 -->
                                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="分辨率:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.OutputResolutionIndex}" Width="150">
                                        <ComboBoxItem Content="保持原分辨率"/>
                                        <ComboBoxItem Content="1920x1080 (横屏)"/>
                                        <ComboBoxItem Content="1280x720 (横屏)"/>
                                        <ComboBoxItem Content="1080x1920 (竖屏)"/>
                                        <ComboBoxItem Content="720x1280 (竖屏)"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- 输出质量 -->
                                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="质量:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.OutputQualityIndex}" Width="70" CornerRadius="3">
                                        <ComboBoxItem Content="原"/>
                                        <ComboBoxItem Content="无损"/>
                                        <ComboBoxItem Content="高"/>
                                        <ComboBoxItem Content="标准"/>
                                        <ComboBoxItem Content="低"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- 输出格式 -->
                                <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="格式:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.OutputFormatIndex}" Width="90">
                                        <ComboBoxItem Content="原格式"/>
                                        <ComboBoxItem Content="MP4"/>
                                        <ComboBoxItem Content="MOV"/>
                                        <ComboBoxItem Content="MKV"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- 是否删除原文件 -->
                                <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="原文件:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.DeleteOriginalIndex}" Width="72">
                                        <ComboBoxItem Content="保留"/>
                                        <ComboBoxItem Content="删除"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- 命名规则 -->
                                <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="命名规则:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.NamingRuleIndex}" Width="100">
                                        <ComboBoxItem Content="原文件名"/>
                                        <ComboBoxItem Content="前缀序号"/>
                                        <ComboBoxItem Content="后缀序号"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- 处理线程选择 -->
                                <StackPanel Grid.Column="5" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="线程:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.ThreadCountIndex}" Width="66">
                                        <ComboBoxItem Content="1"/>
                                        <ComboBoxItem Content="2"/>
                                        <ComboBoxItem Content="3"/>
                                        <ComboBoxItem Content="4"/>
                                        <ComboBoxItem Content="5"/>
                                        <ComboBoxItem Content="6"/>
                                        <ComboBoxItem Content="7"/>
                                        <ComboBoxItem Content="8"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- GPU 加速选择 -->
                                <StackPanel Grid.Column="6" Orientation="Horizontal" Margin="0,0,15,0" VerticalAlignment="Center" Spacing="5">
                                    <TextBlock Text="加速:" VerticalAlignment="Center" FontSize="11" Foreground="#BBBBBB"/>
                                    <ComboBox SelectedIndex="{Binding ProcessingOptions.GpuAccelerationIndex}"
                                              Width="88">
                                        <ComboBoxItem Content="不使用"/>
                                        <ComboBoxItem Content="AMD"/>
                                        <ComboBoxItem Content="NVIDIA"/>
                                        <ComboBoxItem Content="Intel"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <!-- 右侧处理按钮 -->
                                <Button Grid.Column="7" Content="{Binding ProcessButtonText}" Width="120" Height="30" Padding="0"
                                        Classes="StandardButton" Margin="0" VerticalAlignment="Center"
                                        Click="ProcessButton_Click"
                                        Classes.ProcessingButton="{Binding IsProcessing}"
                                        Classes.ReadyButton="{Binding !IsProcessing}"
                                        HorizontalContentAlignment="Center"
                                        VerticalContentAlignment="Center">
                                </Button>
                            </Grid>
                        </Grid>
                        
                        <!-- 分隔线 -->
                        <Border Grid.Row="1" Height="1" Background="#333333" Margin="20,0"/>
                        
                        <!-- 底部进度条区域 -->
                        <Grid Grid.Row="2" Height="36">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 左侧统计信息 -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="15" Margin="20,0" VerticalAlignment="Center">
                                <Grid MinWidth="80">
                                    <StackPanel Orientation="Horizontal" Spacing="5">
                                        <TextBlock Text="总视频数:" FontSize="11" Foreground="#BBBBBB"/>
                                        <TextBlock Text="{Binding TotalVideos}" FontSize="11" Foreground="White" FontWeight="SemiBold"
                                                  TextTrimming="None" TextWrapping="NoWrap"/>
                                    </StackPanel>
                                </Grid>
                                
                                <Grid MinWidth="80">
                                    <StackPanel Orientation="Horizontal" Spacing="5">
                                        <TextBlock Text="已完成:" FontSize="11" Foreground="#BBBBBB"/>
                                        <TextBlock Text="{Binding ProcessedVideos}" FontSize="11" Foreground="#30C9B0" FontWeight="SemiBold"
                                                  TextTrimming="None" TextWrapping="NoWrap"/>
                                    </StackPanel>
                                </Grid>
                                
                                <Grid MinWidth="60">
                                    <StackPanel Orientation="Horizontal" Spacing="5">
                                        <TextBlock Text="失败:" FontSize="11" Foreground="#BBBBBB"/>
                                        <TextBlock Text="{Binding FailedVideos}" FontSize="11" Foreground="#E74C3C" FontWeight="SemiBold"
                                                  TextTrimming="None" TextWrapping="NoWrap"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                            
                            <!-- 丝滑进度显示槽 - 升级版！ -->
                            <Grid Grid.Column="1" VerticalAlignment="Center">
                                <ProgressBar Value="{Binding ProgressManager.DisplayProgress}"
                                             Maximum="100"
                                             Height="8"
                                             CornerRadius="4"
                                             Background="#1A1A1A"
                                             Foreground="#30C9B0"
                                             Margin="0,0,8,0">
                                    <!-- 移除原有过渡，使用丝滑管理器的内置动画 -->
                                </ProgressBar>
                            </Grid>

                            <!-- 丝滑进度百分比 -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="10" Margin="0,0,20,0" VerticalAlignment="Center">
                                <TextBlock Text="{Binding ProgressManager.DisplayProgressText, FallbackValue='0.0%'}" FontSize="11" Foreground="#30C9B0" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>
            </Grid>
            
            <!-- 加载遮罩层 -->
            <Border Grid.RowSpan="2" 
                    Background="#40000000" 
                    IsVisible="{Binding IsLoading}" 
                    ZIndex="1000">
                <Grid HorizontalAlignment="Center" 
                      VerticalAlignment="Center">
                    <StackPanel Spacing="15">
                        <controls:FrameAnimationControl 
                            Name="LoadingAnimation"
                            Width="80" 
                            Height="80" 
                            HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding LoadingMessage}" 
                                   Foreground="White" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>





