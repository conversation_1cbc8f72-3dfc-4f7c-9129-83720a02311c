{"$schema": "https://schema.tauri.app/config/1", "build": {"beforeDevCommand": "npm run dev", "devPath": "http://localhost:1420", "beforeBuildCommand": "npm run build", "distDir": "../dist"}, "package": {"productName": "E剪Pro", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.ecutpro.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "E剪Pro", "width": 1200, "height": 800}]}}