// ******************************************************
// 文件名: FileDialogService.cs
// 功能描述: 文件对话框服务 - 纯文件选择，学习Rust的简洁风格
// 主要职责: 
//   1. 视频文件选择对话框
//   2. 输出文件夹选择对话框
//   3. 文件过滤和验证
// ******************************************************

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Platform.Storage;

namespace ECutPro.Services
{
    /// <summary>
    /// 文件对话框服务 - 单一职责：文件选择
    /// </summary>
    public sealed class FileDialogService
    {
        #region 常量定义
        
        /// <summary>
        /// 支持的视频文件扩展名
        /// </summary>
        private static readonly string[] SupportedVideoExtensions = 
        {
            ".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v", ".3gp", ".ts"
        };
        
        /// <summary>
        /// 视频文件过滤器
        /// </summary>
        private static readonly FilePickerFileType VideoFileType = new("视频文件")
        {
            Patterns = SupportedVideoExtensions.Select(ext => $"*{ext}").ToArray(),
            AppleUniformTypeIdentifiers = new[] { "public.movie" },
            MimeTypes = new[] { "video/*" }
        };
        
        #endregion
        
        #region 文件选择方法
        
        /// <summary>
        /// 选择视频文件（多选）
        /// </summary>
        /// <param name="parentWindow">父窗口</param>
        /// <returns>选择的文件路径列表</returns>
        public async Task<string[]> SelectVideoFilesAsync(Window? parentWindow = null)
        {
            try
            {
                var window = parentWindow ?? App.MainWindow;
                if (window?.StorageProvider == null) return Array.Empty<string>();
                
                var options = new FilePickerOpenOptions
                {
                    Title = "选择视频文件",
                    AllowMultiple = true,
                    FileTypeFilter = new[] { VideoFileType, FilePickerFileTypes.All }
                };
                
                var files = await window.StorageProvider.OpenFilePickerAsync(options);
                
                return files
                    .Where(f => f.Path.IsFile)
                    .Select(f => f.Path.LocalPath)
                    .Where(IsValidVideoFile)
                    .ToArray();
            }
            catch (Exception ex)
            {
                App.ShowToast($"选择文件时出错: {ex.Message}");
                return Array.Empty<string>();
            }
        }
        
        /// <summary>
        /// 选择单个视频文件
        /// </summary>
        /// <param name="parentWindow">父窗口</param>
        /// <returns>选择的文件路径，未选择则返回null</returns>
        public async Task<string?> SelectVideoFileAsync(Window? parentWindow = null)
        {
            var files = await SelectVideoFilesAsync(parentWindow);
            return files.FirstOrDefault();
        }
        
        /// <summary>
        /// 选择输出文件夹
        /// </summary>
        /// <param name="parentWindow">父窗口</param>
        /// <returns>选择的文件夹路径，未选择则返回null</returns>
        public async Task<string?> SelectOutputFolderAsync(Window? parentWindow = null)
        {
            try
            {
                var window = parentWindow ?? App.MainWindow;
                if (window?.StorageProvider == null) return null;
                
                var options = new FolderPickerOpenOptions
                {
                    Title = "选择输出文件夹",
                    AllowMultiple = false
                };
                
                var folders = await window.StorageProvider.OpenFolderPickerAsync(options);
                var folder = folders.FirstOrDefault();
                
                return folder?.Path.LocalPath;
            }
            catch (Exception ex)
            {
                App.ShowToast($"选择文件夹时出错: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 选择保存文件路径
        /// </summary>
        /// <param name="defaultFileName">默认文件名</param>
        /// <param name="parentWindow">父窗口</param>
        /// <returns>选择的保存路径，未选择则返回null</returns>
        public async Task<string?> SelectSaveFileAsync(string defaultFileName = "output.mp4", Window? parentWindow = null)
        {
            try
            {
                var window = parentWindow ?? App.MainWindow;
                if (window?.StorageProvider == null) return null;
                
                var options = new FilePickerSaveOptions
                {
                    Title = "保存视频文件",
                    SuggestedFileName = defaultFileName,
                    FileTypeChoices = new[] { VideoFileType, FilePickerFileTypes.All }
                };
                
                var file = await window.StorageProvider.SaveFilePickerAsync(options);
                return file?.Path.LocalPath;
            }
            catch (Exception ex)
            {
                App.ShowToast($"选择保存路径时出错: {ex.Message}");
                return null;
            }
        }
        
        #endregion
        
        #region 文件验证方法
        
        /// <summary>
        /// 验证是否为有效的视频文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为有效视频文件</returns>
        public static bool IsValidVideoFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath)) return false;
            if (!File.Exists(filePath)) return false;
            
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return SupportedVideoExtensions.Contains(extension);
        }
        
        /// <summary>
        /// 获取支持的视频文件扩展名
        /// </summary>
        /// <returns>支持的扩展名列表</returns>
        public static string[] GetSupportedExtensions()
        {
            return SupportedVideoExtensions.ToArray();
        }
        
        /// <summary>
        /// 获取支持的视频文件格式描述
        /// </summary>
        /// <returns>格式描述字符串</returns>
        public static string GetSupportedFormatsDescription()
        {
            return $"支持的视频格式: {string.Join(", ", SupportedVideoExtensions)}";
        }
        
        /// <summary>
        /// 过滤有效的视频文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>有效的视频文件路径列表</returns>
        public static IEnumerable<string> FilterValidVideoFiles(IEnumerable<string> filePaths)
        {
            return filePaths?.Where(IsValidVideoFile) ?? Enumerable.Empty<string>();
        }
        
        #endregion
    }
}
