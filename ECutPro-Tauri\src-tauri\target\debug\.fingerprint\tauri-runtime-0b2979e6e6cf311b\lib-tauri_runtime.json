{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 8395357155203567578, "deps": [[654232091421095663, "tauri_utils", false, 17397036331854588411], [3150220818285335163, "url", false, 3854908124768576866], [4143744114649553716, "raw_window_handle", false, 4116129918489572412], [7606335748176206944, "dpi", false, 1991029666529341717], [8569119365930580996, "serde_json", false, 10225220210249784331], [9010263965687315507, "http", false, 4609047939896825532], [9689903380558560274, "serde", false, 10466406637492757048], [10806645703491011684, "thiserror", false, 8109794381638202785], [12943761728066819757, "build_script_build", false, 7902435160920809352], [14585479307175734061, "windows", false, 4836215236570752466], [16727543399706004146, "cookie", false, 17680862945110262518]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-0b2979e6e6cf311b\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}