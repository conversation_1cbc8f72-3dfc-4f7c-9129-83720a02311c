{"rustc": 1842507548689473721, "features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"not\", \"rustc_version\", \"sum\", \"try_into\", \"unwrap\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 2225463790103693989, "path": 4762800324406706552, "deps": [[3060637413840920116, "proc_macro2", false, 9960060311121648420], [4974441333307933176, "syn", false, 5768084794600914402], [14907448031486326382, "convert_case", false, 1299797111832224899], [17990358020177143287, "quote", false, 6469833479816154982]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-4f887262a797524a\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}