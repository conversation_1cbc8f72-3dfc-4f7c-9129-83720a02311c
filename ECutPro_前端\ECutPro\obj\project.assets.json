{"version": 3, "targets": {"net9.0": {"Avalonia/11.3.2": {"type": "package", "dependencies": {"Avalonia.BuildServices": "0.0.31", "Avalonia.Remote.Protocol": "11.3.2", "MicroCom.Runtime": "0.11.0"}, "compile": {"ref/net8.0/Avalonia.Base.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Controls.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "ref/net8.0/Avalonia.Metal.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Vulkan.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.Vulkan.xml;.xml"}}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Controls.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "lib/net8.0/Avalonia.Metal.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Vulkan.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.Vulkan.xml;.xml"}}, "build": {"buildTransitive/Avalonia.props": {}, "buildTransitive/Avalonia.targets": {}}}, "Avalonia.Angle.Windows.Natives/2.1.25547.20250602": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-x86"}}}, "Avalonia.BuildServices/0.0.31": {"type": "package", "build": {"buildTransitive/Avalonia.BuildServices.targets": {}}}, "Avalonia.Controls.ColorPicker/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "Avalonia.Remote.Protocol": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"related": ".xml"}}}, "Avalonia.Desktop/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "Avalonia.Native": "11.3.2", "Avalonia.Skia": "11.3.2", "Avalonia.Win32": "11.3.2", "Avalonia.X11": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Desktop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Desktop.dll": {"related": ".xml"}}}, "Avalonia.Diagnostics/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "Avalonia.Controls.ColorPicker": "11.3.2", "Avalonia.Themes.Simple": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"related": ".xml"}}}, "Avalonia.Fonts.Inter/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"related": ".xml"}}}, "Avalonia.FreeDesktop/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "Tmds.DBus.Protocol": "0.21.2"}, "compile": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"related": ".xml"}}}, "Avalonia.Native/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Native.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Native.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"assetType": "native", "rid": "osx"}}}, "Avalonia.Remote.Protocol/11.3.2": {"type": "package", "compile": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}}, "Avalonia.Skia/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "HarfBuzzSharp": "8.3.1.1", "HarfBuzzSharp.NativeAssets.Linux": "8.3.1.1", "HarfBuzzSharp.NativeAssets.WebAssembly": "8.3.1.1", "SkiaSharp": "2.88.9", "SkiaSharp.NativeAssets.Linux": "2.88.9", "SkiaSharp.NativeAssets.WebAssembly": "2.88.9"}, "compile": {"lib/net8.0/Avalonia.Skia.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"related": ".xml"}}}, "Avalonia.Themes.Fluent/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"related": ".xml"}}}, "Avalonia.Themes.Simple/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"related": ".xml"}}}, "Avalonia.Win32/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "Avalonia.Angle.Windows.Natives": "2.1.25547.20250602"}, "compile": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Win32.dll": {"related": ".Automation.xml;.xml"}}, "runtime": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Win32.dll": {"related": ".Automation.xml;.xml"}}}, "Avalonia.X11/11.3.2": {"type": "package", "dependencies": {"Avalonia": "11.3.2", "Avalonia.FreeDesktop": "11.3.2", "Avalonia.Skia": "11.3.2"}, "compile": {"lib/net8.0/Avalonia.X11.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.X11.dll": {"related": ".xml"}}}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "HarfBuzzSharp/8.3.1.1": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "8.3.1.1", "HarfBuzzSharp.NativeAssets.macOS": "8.3.1.1"}, "compile": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-loongarch64"}, "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-loongarch64"}, "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-riscv64"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-riscv64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-riscv64"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x86"}}}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/8.3.1.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "LiteDB/5.0.21": {"type": "package", "dependencies": {"System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/LiteDB.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/LiteDB.dll": {"related": ".xml"}}}, "MicroCom.Runtime/0.11.0": {"type": "package", "compile": {"lib/net5.0/MicroCom.Runtime.dll": {}}, "runtime": {"lib/net5.0/MicroCom.Runtime.dll": {}}}, "Microsoft.DotNet.ILCompiler/9.0.7": {"type": "package", "build": {"build/Microsoft.DotNet.ILCompiler.props": {}}}, "Microsoft.NET.ILLink.Tasks/9.0.7": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"type": "package", "dependencies": {"SkiaSharp": "2.88.9"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "dependencies": {"System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/Tmds.DBus.Protocol.dll": {}}, "runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {}}}}}, "libraries": {"Avalonia/11.3.2": {"sha512": "3w1v4/wEpHFq6WWVNyyrne2Jyz5bdQvnK3AW36rCto42L+AtdbnO/SG2SIqJ7ObXJl+Y3LXz2XDGx9Blzqduow==", "type": "package", "path": "avalonia/11.3.2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Avalonia.Analyzers.dll", "analyzers/dotnet/cs/Avalonia.Generators.dll", "avalonia.11.3.2.nupkg.sha512", "avalonia.nuspec", "build/Avalonia.Generators.props", "build/Avalonia.props", "build/Avalonia.targets", "build/AvaloniaBuildTasks.props", "build/AvaloniaBuildTasks.targets", "build/AvaloniaItemSchema.xaml", "build/AvaloniaPrivateApis.targets", "build/AvaloniaRules.Project.xml", "build/AvaloniaSingleProject.targets", "build/AvaloniaVersion.props", "buildTransitive/Avalonia.Generators.props", "buildTransitive/Avalonia.props", "buildTransitive/Avalonia.targets", "buildTransitive/AvaloniaBuildTasks.props", "buildTransitive/AvaloniaBuildTasks.targets", "buildTransitive/AvaloniaItemSchema.xaml", "buildTransitive/AvaloniaPrivateApis.targets", "buildTransitive/AvaloniaRules.Project.xml", "buildTransitive/AvaloniaSingleProject.targets", "lib/net6.0/Avalonia.Base.dll", "lib/net6.0/Avalonia.Base.xml", "lib/net6.0/Avalonia.Controls.dll", "lib/net6.0/Avalonia.Controls.xml", "lib/net6.0/Avalonia.DesignerSupport.dll", "lib/net6.0/Avalonia.DesignerSupport.xml", "lib/net6.0/Avalonia.Dialogs.dll", "lib/net6.0/Avalonia.Dialogs.xml", "lib/net6.0/Avalonia.Markup.Xaml.dll", "lib/net6.0/Avalonia.Markup.Xaml.xml", "lib/net6.0/Avalonia.Markup.dll", "lib/net6.0/Avalonia.Markup.xml", "lib/net6.0/Avalonia.Metal.dll", "lib/net6.0/Avalonia.Metal.xml", "lib/net6.0/Avalonia.MicroCom.dll", "lib/net6.0/Avalonia.MicroCom.xml", "lib/net6.0/Avalonia.OpenGL.dll", "lib/net6.0/Avalonia.OpenGL.xml", "lib/net6.0/Avalonia.Vulkan.dll", "lib/net6.0/Avalonia.Vulkan.xml", "lib/net6.0/Avalonia.dll", "lib/net6.0/Avalonia.xml", "lib/net8.0/Avalonia.Base.dll", "lib/net8.0/Avalonia.Base.xml", "lib/net8.0/Avalonia.Controls.dll", "lib/net8.0/Avalonia.Controls.xml", "lib/net8.0/Avalonia.DesignerSupport.dll", "lib/net8.0/Avalonia.DesignerSupport.xml", "lib/net8.0/Avalonia.Dialogs.dll", "lib/net8.0/Avalonia.Dialogs.xml", "lib/net8.0/Avalonia.Markup.Xaml.dll", "lib/net8.0/Avalonia.Markup.Xaml.xml", "lib/net8.0/Avalonia.Markup.dll", "lib/net8.0/Avalonia.Markup.xml", "lib/net8.0/Avalonia.Metal.dll", "lib/net8.0/Avalonia.Metal.xml", "lib/net8.0/Avalonia.MicroCom.dll", "lib/net8.0/Avalonia.MicroCom.xml", "lib/net8.0/Avalonia.OpenGL.dll", "lib/net8.0/Avalonia.OpenGL.xml", "lib/net8.0/Avalonia.Vulkan.dll", "lib/net8.0/Avalonia.Vulkan.xml", "lib/net8.0/Avalonia.dll", "lib/net8.0/Avalonia.xml", "lib/netstandard2.0/Avalonia.Base.dll", "lib/netstandard2.0/Avalonia.Base.xml", "lib/netstandard2.0/Avalonia.Controls.dll", "lib/netstandard2.0/Avalonia.Controls.xml", "lib/netstandard2.0/Avalonia.DesignerSupport.dll", "lib/netstandard2.0/Avalonia.DesignerSupport.xml", "lib/netstandard2.0/Avalonia.Dialogs.dll", "lib/netstandard2.0/Avalonia.Dialogs.xml", "lib/netstandard2.0/Avalonia.Markup.Xaml.dll", "lib/netstandard2.0/Avalonia.Markup.Xaml.xml", "lib/netstandard2.0/Avalonia.Markup.dll", "lib/netstandard2.0/Avalonia.Markup.xml", "lib/netstandard2.0/Avalonia.Metal.dll", "lib/netstandard2.0/Avalonia.Metal.xml", "lib/netstandard2.0/Avalonia.MicroCom.dll", "lib/netstandard2.0/Avalonia.MicroCom.xml", "lib/netstandard2.0/Avalonia.OpenGL.dll", "lib/netstandard2.0/Avalonia.OpenGL.xml", "lib/netstandard2.0/Avalonia.Vulkan.dll", "lib/netstandard2.0/Avalonia.Vulkan.xml", "lib/netstandard2.0/Avalonia.dll", "lib/netstandard2.0/Avalonia.xml", "ref/net6.0/Avalonia.Base.dll", "ref/net6.0/Avalonia.Base.xml", "ref/net6.0/Avalonia.Controls.dll", "ref/net6.0/Avalonia.Controls.xml", "ref/net6.0/Avalonia.DesignerSupport.dll", "ref/net6.0/Avalonia.DesignerSupport.xml", "ref/net6.0/Avalonia.Dialogs.dll", "ref/net6.0/Avalonia.Dialogs.xml", "ref/net6.0/Avalonia.Markup.Xaml.dll", "ref/net6.0/Avalonia.Markup.Xaml.xml", "ref/net6.0/Avalonia.Markup.dll", "ref/net6.0/Avalonia.Markup.xml", "ref/net6.0/Avalonia.Metal.dll", "ref/net6.0/Avalonia.Metal.xml", "ref/net6.0/Avalonia.MicroCom.dll", "ref/net6.0/Avalonia.MicroCom.xml", "ref/net6.0/Avalonia.OpenGL.dll", "ref/net6.0/Avalonia.OpenGL.xml", "ref/net6.0/Avalonia.Vulkan.dll", "ref/net6.0/Avalonia.Vulkan.xml", "ref/net6.0/Avalonia.dll", "ref/net6.0/Avalonia.xml", "ref/net8.0/Avalonia.Base.dll", "ref/net8.0/Avalonia.Base.xml", "ref/net8.0/Avalonia.Controls.dll", "ref/net8.0/Avalonia.Controls.xml", "ref/net8.0/Avalonia.DesignerSupport.dll", "ref/net8.0/Avalonia.DesignerSupport.xml", "ref/net8.0/Avalonia.Dialogs.dll", "ref/net8.0/Avalonia.Dialogs.xml", "ref/net8.0/Avalonia.Markup.Xaml.dll", "ref/net8.0/Avalonia.Markup.Xaml.xml", "ref/net8.0/Avalonia.Markup.dll", "ref/net8.0/Avalonia.Markup.xml", "ref/net8.0/Avalonia.Metal.dll", "ref/net8.0/Avalonia.Metal.xml", "ref/net8.0/Avalonia.MicroCom.dll", "ref/net8.0/Avalonia.MicroCom.xml", "ref/net8.0/Avalonia.OpenGL.dll", "ref/net8.0/Avalonia.OpenGL.xml", "ref/net8.0/Avalonia.Vulkan.dll", "ref/net8.0/Avalonia.Vulkan.xml", "ref/net8.0/Avalonia.dll", "ref/net8.0/Avalonia.xml", "ref/netstandard2.0/Avalonia.Base.dll", "ref/netstandard2.0/Avalonia.Base.xml", "ref/netstandard2.0/Avalonia.Controls.dll", "ref/netstandard2.0/Avalonia.Controls.xml", "ref/netstandard2.0/Avalonia.DesignerSupport.dll", "ref/netstandard2.0/Avalonia.DesignerSupport.xml", "ref/netstandard2.0/Avalonia.Dialogs.dll", "ref/netstandard2.0/Avalonia.Dialogs.xml", "ref/netstandard2.0/Avalonia.Markup.Xaml.dll", "ref/netstandard2.0/Avalonia.Markup.Xaml.xml", "ref/netstandard2.0/Avalonia.Markup.dll", "ref/netstandard2.0/Avalonia.Markup.xml", "ref/netstandard2.0/Avalonia.Metal.dll", "ref/netstandard2.0/Avalonia.Metal.xml", "ref/netstandard2.0/Avalonia.MicroCom.dll", "ref/netstandard2.0/Avalonia.MicroCom.xml", "ref/netstandard2.0/Avalonia.OpenGL.dll", "ref/netstandard2.0/Avalonia.OpenGL.xml", "ref/netstandard2.0/Avalonia.Vulkan.dll", "ref/netstandard2.0/Avalonia.Vulkan.xml", "ref/netstandard2.0/Avalonia.dll", "ref/netstandard2.0/Avalonia.xml", "tools/net461/designer/Avalonia.Designer.HostApp.exe", "tools/netstandard2.0/Avalonia.Build.Tasks.dll", "tools/netstandard2.0/designer/Avalonia.Designer.HostApp.dll"]}, "Avalonia.Angle.Windows.Natives/2.1.25547.20250602": {"sha512": "ZL0VLc4s9rvNNFt19Pxm5UNAkmKNylugAwJPX9ulXZ6JWs/l6XZihPWWTyezaoNOVyEPU8YbURtW7XMAtqXH5A==", "type": "package", "path": "avalonia.angle.windows.natives/2.1.25547.20250602", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE", "avalonia.angle.windows.natives.2.1.25547.20250602.nupkg.sha512", "avalonia.angle.windows.natives.nuspec", "runtimes/win-arm64/native/av_libglesv2.dll", "runtimes/win-x64/native/av_libglesv2.dll", "runtimes/win-x86/native/av_libglesv2.dll"]}, "Avalonia.BuildServices/0.0.31": {"sha512": "KmCN6Hc+45q4OnF10ge450yVUvWuxU6bdQiyKqiSvrHKpahNrEdk0kG6Ip6GHk2SKOCttGQuA206JVdkldEENg==", "type": "package", "path": "avalonia.buildservices/0.0.31", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.buildservices.0.0.31.nupkg.sha512", "avalonia.buildservices.nuspec", "build/Avalonia.BuildServices.targets", "buildTransitive/Avalonia.BuildServices.targets", "tools/netstandard2.0/Avalonia.BuildServices.Collector.dll", "tools/netstandard2.0/Avalonia.BuildServices.dll", "tools/netstandard2.0/runtimeconfig.json"]}, "Avalonia.Controls.ColorPicker/11.3.2": {"sha512": "K72rpZb6nSDuKZtcj1cfIAqpTkduff3Ng3+O22MxKhmmRDcFO0GAz7kwEArbtJTC4SNlSezaCyx6XMdNvaMcPA==", "type": "package", "path": "avalonia.controls.colorpicker/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.controls.colorpicker.11.3.2.nupkg.sha512", "avalonia.controls.colorpicker.nuspec", "lib/net6.0/Avalonia.Controls.ColorPicker.dll", "lib/net6.0/Avalonia.Controls.ColorPicker.xml", "lib/net8.0/Avalonia.Controls.ColorPicker.dll", "lib/net8.0/Avalonia.Controls.ColorPicker.xml", "lib/netstandard2.0/Avalonia.Controls.ColorPicker.dll", "lib/netstandard2.0/Avalonia.Controls.ColorPicker.xml"]}, "Avalonia.Desktop/11.3.2": {"sha512": "b2ZPZ60UN0Uib3ZxFrf+ixYiJTBFaXrpQAcXTSUKTDw0nAU29sbfjvdtpBkBd6+idWpNtI+GhOjf0Mw0v1ncQg==", "type": "package", "path": "avalonia.desktop/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.desktop.11.3.2.nupkg.sha512", "avalonia.desktop.nuspec", "lib/net6.0/Avalonia.Desktop.dll", "lib/net6.0/Avalonia.Desktop.xml", "lib/net8.0/Avalonia.Desktop.dll", "lib/net8.0/Avalonia.Desktop.xml", "lib/netstandard2.0/Avalonia.Desktop.dll", "lib/netstandard2.0/Avalonia.Desktop.xml"]}, "Avalonia.Diagnostics/11.3.2": {"sha512": "3f4+uGJTrBCY9mxMy7690s8WKqyF4I29VFEUSASV5nVX6kyv/d1+OHBNd0GMyuyPOf+eEEA1ylNSHhRCw3jsvw==", "type": "package", "path": "avalonia.diagnostics/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.diagnostics.11.3.2.nupkg.sha512", "avalonia.diagnostics.nuspec", "lib/net6.0/Avalonia.Diagnostics.dll", "lib/net6.0/Avalonia.Diagnostics.xml", "lib/net8.0/Avalonia.Diagnostics.dll", "lib/net8.0/Avalonia.Diagnostics.xml", "lib/netstandard2.0/Avalonia.Diagnostics.dll", "lib/netstandard2.0/Avalonia.Diagnostics.xml"]}, "Avalonia.Fonts.Inter/11.3.2": {"sha512": "gOijv2Tu8w6XurxYWSC4OdFycYbDooZfTcETXEx45syl710wpG+0aEHNFGlQyyOqqxGQHFbqxxwX6GxumD8b/g==", "type": "package", "path": "avalonia.fonts.inter/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.fonts.inter.11.3.2.nupkg.sha512", "avalonia.fonts.inter.nuspec", "lib/net6.0/Avalonia.Fonts.Inter.dll", "lib/net6.0/Avalonia.Fonts.Inter.xml", "lib/net8.0/Avalonia.Fonts.Inter.dll", "lib/net8.0/Avalonia.Fonts.Inter.xml", "lib/netstandard2.0/Avalonia.Fonts.Inter.dll", "lib/netstandard2.0/Avalonia.Fonts.Inter.xml"]}, "Avalonia.FreeDesktop/11.3.2": {"sha512": "8laGOvPM83kB4xUqXGIRxN61Q5Ux/97URL7CU2Dizh4Nm7Ydj9+urKLTylk6dxWG7kZlo4e4k9G2aVUPkxTBGA==", "type": "package", "path": "avalonia.freedesktop/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.freedesktop.11.3.2.nupkg.sha512", "avalonia.freedesktop.nuspec", "lib/net6.0/Avalonia.FreeDesktop.dll", "lib/net6.0/Avalonia.FreeDesktop.xml", "lib/net8.0/Avalonia.FreeDesktop.dll", "lib/net8.0/Avalonia.FreeDesktop.xml", "lib/netstandard2.0/Avalonia.FreeDesktop.dll", "lib/netstandard2.0/Avalonia.FreeDesktop.xml"]}, "Avalonia.Native/11.3.2": {"sha512": "dv3PVUcClxeDlNSoBjTdNuRYXdbzT3BgtpjEX/fv3pVreKfbh39wWQ+n4ecGh0FUKIlcj0X8BEaQ83t5eRsLnA==", "type": "package", "path": "avalonia.native/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.native.11.3.2.nupkg.sha512", "avalonia.native.nuspec", "lib/net6.0/Avalonia.Native.dll", "lib/net6.0/Avalonia.Native.xml", "lib/net8.0/Avalonia.Native.dll", "lib/net8.0/Avalonia.Native.xml", "lib/netstandard2.0/Avalonia.Native.dll", "lib/netstandard2.0/Avalonia.Native.xml", "runtimes/osx/native/libAvaloniaNative.dylib"]}, "Avalonia.Remote.Protocol/11.3.2": {"sha512": "my6aXePR+N7tl3xDKdYNH2ZFZWUSNmyeU37HIgYJX2fQ4IOBv7SeaWBEd1F/qVsIbOlYxaqE7qOPfeFFtdYq/A==", "type": "package", "path": "avalonia.remote.protocol/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.remote.protocol.11.3.2.nupkg.sha512", "avalonia.remote.protocol.nuspec", "lib/net6.0/Avalonia.Remote.Protocol.dll", "lib/net6.0/Avalonia.Remote.Protocol.xml", "lib/net8.0/Avalonia.Remote.Protocol.dll", "lib/net8.0/Avalonia.Remote.Protocol.xml", "lib/netstandard2.0/Avalonia.Remote.Protocol.dll", "lib/netstandard2.0/Avalonia.Remote.Protocol.xml"]}, "Avalonia.Skia/11.3.2": {"sha512": "PJIPSqkWbQpKyWPMEIDAWYQaJ8xpJASg2H75sGWCkthLj+mCG/DZxdHI6UsDAcxKu/ppSRkX88RGo117bRYfFg==", "type": "package", "path": "avalonia.skia/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.skia.11.3.2.nupkg.sha512", "avalonia.skia.nuspec", "lib/net6.0/Avalonia.Skia.dll", "lib/net6.0/Avalonia.Skia.xml", "lib/net8.0/Avalonia.Skia.dll", "lib/net8.0/Avalonia.Skia.xml", "lib/netstandard2.0/Avalonia.Skia.dll", "lib/netstandard2.0/Avalonia.Skia.xml"]}, "Avalonia.Themes.Fluent/11.3.2": {"sha512": "aRCCmFSumpHJOxsXbdUUc/AaJ/O1HLggfvSxtXYm84QCutuP4OCqgxP9Pka1hb1+2e/TQxfNqM/6KRKPt4SaRg==", "type": "package", "path": "avalonia.themes.fluent/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.themes.fluent.11.3.2.nupkg.sha512", "avalonia.themes.fluent.nuspec", "lib/net6.0/Avalonia.Themes.Fluent.dll", "lib/net6.0/Avalonia.Themes.Fluent.xml", "lib/net8.0/Avalonia.Themes.Fluent.dll", "lib/net8.0/Avalonia.Themes.Fluent.xml", "lib/netstandard2.0/Avalonia.Themes.Fluent.dll", "lib/netstandard2.0/Avalonia.Themes.Fluent.xml"]}, "Avalonia.Themes.Simple/11.3.2": {"sha512": "0TnR6vVS5qStClhG0T9i5Q7jSlZxqrpBxzBb7HPzNqe8dkNfM6VTN38J82sggvZAVddqSc3XRrB+VgheChxKQw==", "type": "package", "path": "avalonia.themes.simple/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.themes.simple.11.3.2.nupkg.sha512", "avalonia.themes.simple.nuspec", "lib/net6.0/Avalonia.Themes.Simple.dll", "lib/net6.0/Avalonia.Themes.Simple.xml", "lib/net8.0/Avalonia.Themes.Simple.dll", "lib/net8.0/Avalonia.Themes.Simple.xml", "lib/netstandard2.0/Avalonia.Themes.Simple.dll", "lib/netstandard2.0/Avalonia.Themes.Simple.xml"]}, "Avalonia.Win32/11.3.2": {"sha512": "CpM6zBwDwMFKw9/iyj0d8jxXKLZena/HFblS9Oc7BmLH8qxe8icr7ZBI5rElebrxDA5O590dAtfWdtOvB1/AZQ==", "type": "package", "path": "avalonia.win32/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.win32.11.3.2.nupkg.sha512", "avalonia.win32.nuspec", "lib/net6.0/Avalonia.Win32.Automation.dll", "lib/net6.0/Avalonia.Win32.Automation.xml", "lib/net6.0/Avalonia.Win32.dll", "lib/net6.0/Avalonia.Win32.xml", "lib/net8.0/Avalonia.Win32.Automation.dll", "lib/net8.0/Avalonia.Win32.Automation.xml", "lib/net8.0/Avalonia.Win32.dll", "lib/net8.0/Avalonia.Win32.xml", "lib/netstandard2.0/Avalonia.Win32.Automation.dll", "lib/netstandard2.0/Avalonia.Win32.Automation.xml", "lib/netstandard2.0/Avalonia.Win32.dll", "lib/netstandard2.0/Avalonia.Win32.xml"]}, "Avalonia.X11/11.3.2": {"sha512": "MEMXOIaAr6jMtl9BhJ8sj7Vz+z19dWfcryWksV35LsolQhQmjmzJPVcCppdnvm5HxkohUXB1qN1RqWN1cqRwBQ==", "type": "package", "path": "avalonia.x11/11.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.x11.11.3.2.nupkg.sha512", "avalonia.x11.nuspec", "lib/net6.0/Avalonia.X11.dll", "lib/net6.0/Avalonia.X11.xml", "lib/net8.0/Avalonia.X11.dll", "lib/net8.0/Avalonia.X11.xml", "lib/netstandard2.0/Avalonia.X11.dll", "lib/netstandard2.0/Avalonia.X11.xml"]}, "CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "HarfBuzzSharp/8.3.1.1": {"sha512": "tLZN66oe/uiRPTZfrCU4i8ScVGwqHNh5MHrXj0yVf4l7Mz0FhTGnQ71RGySROTmdognAs0JtluHkL41pIabWuQ==", "type": "package", "path": "harfbuzzsharp/8.3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "harfbuzzsharp.8.3.1.1.nupkg.sha512", "harfbuzzsharp.nuspec", "icon.png", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net8.0-android34.0/HarfBuzzSharp.dll", "lib/net8.0-android34.0/HarfBuzzSharp.pdb", "lib/net8.0-android34.0/HarfBuzzSharp.xml", "lib/net8.0-ios17.0/HarfBuzzSharp.dll", "lib/net8.0-ios17.0/HarfBuzzSharp.pdb", "lib/net8.0-maccatalyst17.0/HarfBuzzSharp.dll", "lib/net8.0-maccatalyst17.0/HarfBuzzSharp.pdb", "lib/net8.0-macos14.0/HarfBuzzSharp.dll", "lib/net8.0-macos14.0/HarfBuzzSharp.pdb", "lib/net8.0-tizen7.0/HarfBuzzSharp.dll", "lib/net8.0-tizen7.0/HarfBuzzSharp.pdb", "lib/net8.0-tvos17.0/HarfBuzzSharp.dll", "lib/net8.0-tvos17.0/HarfBuzzSharp.pdb", "lib/net8.0-windows10.0.19041/HarfBuzzSharp.dll", "lib/net8.0-windows10.0.19041/HarfBuzzSharp.pdb", "lib/net8.0/HarfBuzzSharp.dll", "lib/net8.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb"]}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1": {"sha512": "3EZ1mpIiKWRLL5hUYA82ZHteeDIVaEA/Z0rA/wU6tjx6crcAkJnBPwDXZugBSfo8+J3EznvRJf49uMsqYfKrHg==", "type": "package", "path": "harfbuzzsharp.nativeassets.linux/8.3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "harfbuzzsharp.nativeassets.linux.8.3.1.1.nupkg.sha512", "harfbuzzsharp.nativeassets.linux.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/linux-arm/native/libHarfBuzzSharp.so", "runtimes/linux-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so", "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "runtimes/linux-riscv64/native/libHarfBuzzSharp.so", "runtimes/linux-x64/native/libHarfBuzzSharp.so", "runtimes/linux-x86/native/libHarfBuzzSharp.so"]}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1": {"sha512": "jbtCsgftcaFLCA13tVKo5iWdElJScrulLTKJre36O4YQTIlwDtPPqhRZNk+Y0vv4D1gxbscasGRucUDfS44ofQ==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/8.3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.8.3.1.1.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.WebAssembly/8.3.1.1": {"sha512": "loJweK2u/mH/3C2zBa0ggJlITIszOkK64HLAZB7FUT670dTg965whLFYHDQo69NmC4+d9UN0icLC9VHidXaVCA==", "type": "package", "path": "harfbuzzsharp.nativeassets.webassembly/8.3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/2.0.23/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/2.0.6/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt,simd/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.12/st,simd/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.12/st/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.34/mt,simd/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.34/mt/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.34/st,simd/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.34/st/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.56/mt,simd/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.56/mt/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.56/st,simd/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.56/st/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/libHarfBuzzSharp.a/3.1.7/libHarfBuzzSharp.a", "harfbuzzsharp.nativeassets.webassembly.8.3.1.1.nupkg.sha512", "harfbuzzsharp.nativeassets.webassembly.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._"]}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1": {"sha512": "UsJtQsfAJoFDZrXc4hCUfRPMqccfKZ0iumJ/upcUjz/cmsTgVFGNEL5yaJWmkqsuFYdMWbj/En5/kS4PFl9hBA==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/8.3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.8.3.1.1.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "LiteDB/5.0.21": {"sha512": "ykJ7ffFl7P9YQKR/PLci6zupiLrsSCNkOTiw6OtzntH7d2kCYp5L1+3a/pksKgTEHcJBoPXFtg7VZSGVBseN9w==", "type": "package", "path": "litedb/5.0.21", "files": [".nupkg.metadata", ".signature.p7s", "AppVeyorSettings.json", "LICENSE", "icon_64x64.png", "lib/net45/LiteDB.dll", "lib/net45/LiteDB.xml", "lib/netstandard1.3/LiteDB.dll", "lib/netstandard1.3/LiteDB.xml", "lib/netstandard2.0/LiteDB.dll", "lib/netstandard2.0/LiteDB.xml", "litedb.5.0.21.nupkg.sha512", "litedb.nuspec"]}, "MicroCom.Runtime/0.11.0": {"sha512": "MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "type": "package", "path": "microcom.runtime/0.11.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/MicroCom.Runtime.dll", "lib/netstandard2.0/MicroCom.Runtime.dll", "microcom.runtime.0.11.0.nupkg.sha512", "microcom.runtime.nuspec"]}, "Microsoft.DotNet.ILCompiler/9.0.7": {"sha512": "FpvJ2rCpj2CB/4hZt0cmB3hCPMNZV2vrisQis3hR9itTEjLzeggi0AShrgJIZ5+3Y7433K7ESSsJ6+8T+nE2nQ==", "type": "package", "path": "microsoft.dotnet.ilcompiler/9.0.7", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natstepfilter", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "microsoft.dotnet.ilcompiler.9.0.7.nupkg.sha512", "microsoft.dotnet.ilcompiler.nuspec", "runtime.json", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll"]}, "Microsoft.NET.ILLink.Tasks/9.0.7": {"sha512": "SZ1brSGoLnhLbE8QUZrtN6YwzN2gDT1wbx9qDBEfFFJcstiDTjJ6ygNuTPBV/K7SjGfx2YNbcJi5+ygbPOZpDg==", "type": "package", "path": "microsoft.net.illink.tasks/9.0.7", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.9.0.7.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net9.0/ILLink.Tasks.deps.json", "tools/net9.0/ILLink.Tasks.dll", "tools/net9.0/Mono.Cecil.Mdb.dll", "tools/net9.0/Mono.Cecil.Pdb.dll", "tools/net9.0/Mono.Cecil.Rocks.dll", "tools/net9.0/Mono.Cecil.dll", "tools/net9.0/Sdk/Sdk.props", "tools/net9.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net9.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net9.0/build/Microsoft.NET.ILLink.targets", "tools/net9.0/illink.deps.json", "tools/net9.0/illink.dll", "tools/net9.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "SkiaSharp/2.88.9": {"sha512": "3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "type": "package", "path": "skiasharp/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.9.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"sha512": "cWSaJKVPWAaT/WIn9c8T5uT/l4ETwHxNJTkEOtNKjphNo8AW6TF9O32aRkxqw3l8GUdUo66Bu7EiqtFh/XG0Zg==", "type": "package", "path": "skiasharp.nativeassets.linux/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.2.88.9.nupkg.sha512", "skiasharp.nativeassets.linux.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"sha512": "Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {"sha512": "kt06RccBHSnAs2wDYdBSfsjIDbY3EpsOVqnlDgKdgvyuRA8ZFDaHRdWNx1VHjGgYzmnFCGiTJBnXFl5BqGwGnA==", "type": "package", "path": "skiasharp.nativeassets.webassembly/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libSkiaSharp.a/2.0.23/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/2.0.6/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt,simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/simd,mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/simd,st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.56/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.7/libSkiaSharp.a", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "lib/netstandard1.0/_._", "skiasharp.nativeassets.webassembly.2.88.9.nupkg.sha512", "skiasharp.nativeassets.webassembly.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"sha512": "wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "Tmds.DBus.Protocol/0.21.2": {"sha512": "ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "type": "package", "path": "tmds.dbus.protocol/0.21.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Tmds.DBus.Protocol.dll", "lib/net8.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.1/Tmds.DBus.Protocol.dll", "tmds.dbus.protocol.0.21.2.nupkg.sha512", "tmds.dbus.protocol.nuspec"]}}, "projectFileDependencyGroups": {"net9.0": ["Avalonia >= 11.3.2", "Avalonia.Desktop >= 11.3.2", "Avalonia.Diagnostics >= 11.3.2", "Avalonia.Fonts.Inter >= 11.3.2", "Avalonia.Themes.Fluent >= 11.3.2", "CommunityToolkit.Mvvm >= 8.4.0", "LiteDB >= 5.0.21", "Microsoft.DotNet.ILCompiler >= 9.0.7", "Microsoft.NET.ILLink.Tasks >= 9.0.7"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\APP\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\ECutPro.csproj", "projectName": "ECutPro", "projectPath": "E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\ECutPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\APP\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.2, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "LiteDB": {"target": "Package", "version": "[5.0.21, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}